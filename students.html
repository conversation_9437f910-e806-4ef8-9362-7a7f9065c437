<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة التلاميذ - مؤسسة النور التربوي</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">
    <style>
        .students-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .students-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .students-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-icon {
            font-size: 2.5em;
            margin-bottom: 15px;
            color: #667eea;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .students-controls {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .controls-row {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        .search-group {
            flex: 1;
            min-width: 200px;
        }
        
        .search-group input, .search-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .search-group input:focus, .search-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        
        .btn-info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .students-table-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .students-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .students-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: 600;
        }
        
        .students-table td {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #e1e5e9;
            vertical-align: middle;
        }
        
        .students-table tbody tr:hover {
            background-color: #f8f9fa;
        }
        
        .student-photo {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #667eea;
        }
        
        .student-barcode {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            background: #f8f9fa;
            padding: 5px 10px;
            border-radius: 5px;
            border: 1px solid #e1e5e9;
        }
        
        .payment-status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .status-paid {
            background: #d4edda;
            color: #155724;
        }
        
        .status-partial {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-unpaid {
            background: #f8d7da;
            color: #721c24;
        }
        
        .action-btn {
            padding: 8px 12px;
            margin: 2px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .btn-edit {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-delete {
            background: #dc3545;
            color: white;
        }
        
        .btn-pay {
            background: #28a745;
            color: white;
        }
        
        .btn-print {
            background: #17a2b8;
            color: white;
        }
        
        .action-btn:hover {
            transform: scale(1.05);
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease;
        }
        
        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e1e5e9;
        }
        
        .modal-title {
            font-size: 1.5em;
            font-weight: bold;
            color: #333;
        }
        
        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #aaa;
            transition: color 0.3s ease;
        }
        
        .close:hover {
            color: #333;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .photo-upload {
            text-align: center;
            padding: 20px;
            border: 2px dashed #e1e5e9;
            border-radius: 8px;
            transition: border-color 0.3s ease;
        }
        
        .photo-upload:hover {
            border-color: #667eea;
        }
        
        .photo-preview {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 10px;
            border: 3px solid #667eea;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .empty-state i {
            font-size: 4em;
            color: #ccc;
            margin-bottom: 20px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            font-weight: 600;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        @media (max-width: 768px) {
            .students-container {
                padding: 10px;
            }
            
            .controls-row {
                flex-direction: column;
            }
            
            .search-group {
                min-width: 100%;
            }
            
            .action-buttons {
                width: 100%;
                justify-content: center;
            }
            
            .students-table {
                font-size: 12px;
            }
            
            .students-table th,
            .students-table td {
                padding: 8px;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header>
        <img src="logo.png" alt="شعار مؤسسة النور التربوي" class="logo">
        <h1>مؤسسة النور التربوي للتعليم الخصوصي</h1>
        <nav>
            <ul>
                <li><a href="index.html">لوحة التحكم</a></li>
                <li><a href="students.html" class="active">إدارة التلاميذ</a></li>
                <li><a href="teachers.html">إدارة الأساتذة</a></li>
                <li><a href="staff.html">إدارة الموظفين</a></li>
                <li><a href="groups.html">إدارة المستويات والأفواج</a></li>
                <li><a href="financial.html">الإجراءات المالية</a></li>
                <li><a href="payments.html">عمليات الدفع</a></li>
                <li><a href="whatsapp.html">إدارة WhatsApp</a></li>
                <li><a href="import-export.html">الاستيراد والتصدير</a></li>
                <li><a href="users.html" data-permission="users_read">إدارة المستخدمين</a></li>
                <li><a href="activities.html">إدارة الأنشطة</a></li>
                <li><a href="settings.html">الإعدادات</a></li>
            </ul>
        </nav>
        <div class="user-info">
            <span class="current-user-name">المدير</span>
            <span class="current-user-role">مدير</span>
            <button onclick="window.authSystem.logout()" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </button>
        </div>
    </header>

    <main>
        <div class="students-container">
            <!-- رأس الصفحة -->
            <div class="students-header">
                <h2><i class="fas fa-graduation-cap"></i> إدارة التلاميذ</h2>
                <p>نظام شامل لإدارة بيانات التلاميذ والمدفوعات والأنشطة</p>
            </div>

            <!-- الإحصائيات -->
            <div class="students-stats">
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-users"></i></div>
                    <div class="stat-number" id="total-students">0</div>
                    <div class="stat-label">إجمالي التلاميذ</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-check-circle"></i></div>
                    <div class="stat-number" id="paid-students">0</div>
                    <div class="stat-label">مدفوع بالكامل</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-exclamation-triangle"></i></div>
                    <div class="stat-number" id="partial-students">0</div>
                    <div class="stat-label">مدفوع جزئياً</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-times-circle"></i></div>
                    <div class="stat-number" id="unpaid-students">0</div>
                    <div class="stat-label">غير مدفوع</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-money-bill-wave"></i></div>
                    <div class="stat-number" id="total-revenue">0</div>
                    <div class="stat-label">إجمالي الإيرادات (DH)</div>
                </div>
            </div>

            <!-- أدوات التحكم -->
            <div class="students-controls">
                <div class="controls-row">
                    <div class="search-group">
                        <input type="text" id="search-name" placeholder="البحث بالاسم...">
                    </div>
                    <div class="search-group">
                        <select id="search-level">
                            <option value="">جميع المستويات</option>
                        </select>
                    </div>
                    <div class="search-group">
                        <select id="search-group">
                            <option value="">جميع الأفواج</option>
                        </select>
                    </div>
                    <div class="search-group">
                        <input type="text" id="search-barcode" placeholder="البحث بالرقم التسلسلي...">
                    </div>
                </div>
                
                <div class="controls-row">
                    <div class="action-buttons">
                        <button class="btn btn-primary" onclick="openAddStudentModal()">
                            <i class="fas fa-plus"></i> إضافة تلميذ جديد
                        </button>
                        <button class="btn btn-success" onclick="openImportModal()">
                            <i class="fas fa-file-excel"></i> استيراد من Excel
                        </button>
                        <button class="btn btn-info" onclick="downloadExcelTemplate()">
                            <i class="fas fa-download"></i> تحميل نموذج Excel
                        </button>
                        <button class="btn btn-info" onclick="exportStudents()">
                            <i class="fas fa-download"></i> تصدير البيانات
                        </button>
                        <button class="btn btn-warning" onclick="printStudentsList()">
                            <i class="fas fa-print"></i> طباعة القائمة
                        </button>
                    </div>
                </div>
            </div>

            <!-- جدول التلاميذ -->
            <div class="students-table-container">
                <div id="loading" class="loading" style="display: none;">
                    <div class="spinner"></div>
                    <p>جاري تحميل البيانات...</p>
                </div>
                
                <div id="students-content">
                    <table class="students-table" id="students-table">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>الرقم التسلسلي</th>
                                <th>الاسم الكامل</th>
                                <th>المستوى</th>
                                <th>الفوج</th>
                                <th>رقم الهاتف</th>
                                <th>الرسوم الشهرية</th>
                                <th>المدفوع</th>
                                <th>المتبقي</th>
                                <th>حالة الدفع</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="students-table-body">
                            <!-- سيتم ملء البيانات هنا -->
                        </tbody>
                    </table>
                    
                    <div id="empty-state" class="empty-state" style="display: none;">
                        <i class="fas fa-users"></i>
                        <h3>لا توجد بيانات تلاميذ</h3>
                        <p>ابدأ بإضافة التلاميذ لعرضهم هنا</p>
                        <button class="btn btn-primary" onclick="openAddStudentModal()">
                            <i class="fas fa-plus"></i> إضافة أول تلميذ
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- نافذة إضافة/تعديل تلميذ -->
    <div id="student-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modal-title">إضافة تلميذ جديد</h3>
                <span class="close" onclick="closeStudentModal()">&times;</span>
            </div>

            <form id="student-form">
                <div class="form-group">
                    <label>صورة التلميذ</label>
                    <div class="photo-upload" onclick="document.getElementById('student-photo').click()">
                        <img id="photo-preview" class="photo-preview" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik01MCA1OEM1Ni42Mjc0IDU4IDYyIDUyLjYyNzQgNjIgNDZDNjIgMzkuMzcyNiA1Ni42Mjc0IDM0IDUwIDM0QzQzLjM3MjYgMzQgMzggMzkuMzcyNiAzOCA0NkMzOCA1Mi42Mjc0IDQzLjM3MjYgNTggNTAgNThaIiBmaWxsPSIjOUI5QkEwIi8+CjxwYXRoIGQ9Ik0yNiA3NEMyNiA2NS4xNjM0IDMzLjE2MzQgNTggNDIgNThINThDNjYuODM2NiA1OCA3NCA2NS4xNjM0IDc0IDc0VjgySDI2Vjc0WiIgZmlsbD0iIzlCOUJBMCIvPgo8L3N2Zz4K" alt="صورة التلميذ">
                        <p>انقر لاختيار صورة</p>
                        <input type="file" id="student-photo" accept="image/*" style="display: none;" onchange="previewPhoto(this)">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="student-name">الاسم الكامل *</label>
                        <input type="text" id="student-name" required>
                    </div>
                    <div class="form-group">
                        <label for="student-birth-date">تاريخ الميلاد</label>
                        <input type="date" id="student-birth-date">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="student-level">المستوى *</label>
                        <select id="student-level" required onchange="updateGroupOptions()">
                            <option value="">اختر المستوى</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="student-group">الفوج *</label>
                        <select id="student-group" required>
                            <option value="">اختر الفوج</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="student-phone">رقم هاتف ولي الأمر *</label>
                        <input type="tel" id="student-phone" required>
                    </div>
                    <div class="form-group">
                        <label for="student-email">البريد الإلكتروني</label>
                        <input type="email" id="student-email">
                    </div>
                </div>

                <div class="form-group">
                    <label for="student-address">العنوان</label>
                    <textarea id="student-address" rows="3"></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="student-fee">الرسوم الشهرية (DH) *</label>
                        <input type="number" id="student-fee" min="0" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label for="student-transport">رسوم النقل (DH)</label>
                        <input type="number" id="student-transport" min="0" step="0.01" value="0">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="student-paid">المبلغ المدفوع (DH)</label>
                        <input type="number" id="student-paid" min="0" step="0.01" value="0">
                    </div>
                    <div class="form-group">
                        <label for="student-discount">الخصم (DH)</label>
                        <input type="number" id="student-discount" min="0" step="0.01" value="0">
                    </div>
                </div>

                <div class="form-group">
                    <label for="student-notes">ملاحظات</label>
                    <textarea id="student-notes" rows="3"></textarea>
                </div>

                <div class="form-group" style="text-align: center; margin-top: 30px;">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ البيانات
                    </button>
                    <button type="button" class="btn" onclick="closeStudentModal()" style="background: #6c757d; color: white; margin-right: 10px;">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة الدفع -->
    <div id="payment-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">تسجيل دفعة جديدة</h3>
                <span class="close" onclick="closePaymentModal()">&times;</span>
            </div>

            <div id="payment-student-info" class="alert alert-info">
                <!-- معلومات التلميذ -->
            </div>

            <form id="payment-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="payment-amount">مبلغ الدفعة (DH) *</label>
                        <input type="number" id="payment-amount" min="0" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label for="payment-method">طريقة الدفع</label>
                        <select id="payment-method">
                            <option value="cash">نقداً</option>
                            <option value="bank">تحويل بنكي</option>
                            <option value="check">شيك</option>
                            <option value="card">بطاقة ائتمان</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="payment-date">تاريخ الدفع</label>
                    <input type="date" id="payment-date">
                </div>

                <div class="form-group">
                    <label for="payment-notes">ملاحظات الدفعة</label>
                    <textarea id="payment-notes" rows="3"></textarea>
                </div>

                <div class="form-group" style="text-align: center; margin-top: 30px;">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-money-bill-wave"></i> تسجيل الدفعة
                    </button>
                    <button type="button" class="btn" onclick="closePaymentModal()" style="background: #6c757d; color: white; margin-right: 10px;">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة النفقات اليومية -->
    <div id="expenses-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">إدارة النفقات اليومية</h3>
                <span class="close" onclick="closeExpensesModal()">&times;</span>
            </div>

            <div id="expenses-student-info" class="alert alert-info">
                <!-- معلومات التلميذ -->
            </div>

            <!-- إضافة نفقة جديدة -->
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                <h4>إضافة نفقة جديدة</h4>
                <form id="expense-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="expense-type">نوع النفقة *</label>
                            <select id="expense-type" required>
                                <option value="">اختر نوع النفقة</option>
                                <option value="meals">وجبات</option>
                                <option value="transport">نقل</option>
                                <option value="books">كتب ومستلزمات</option>
                                <option value="activities">أنشطة</option>
                                <option value="medical">طبية</option>
                                <option value="uniform">زي مدرسي</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="expense-amount">المبلغ (DH) *</label>
                            <input type="number" id="expense-amount" min="0" step="0.01" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="expense-date">التاريخ</label>
                            <input type="date" id="expense-date">
                        </div>
                        <div class="form-group">
                            <label for="expense-paid-by">دفع بواسطة</label>
                            <select id="expense-paid-by">
                                <option value="student">التلميذ</option>
                                <option value="parent">ولي الأمر</option>
                                <option value="school">المدرسة</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="expense-description">الوصف</label>
                        <textarea id="expense-description" rows="2" placeholder="تفاصيل النفقة..."></textarea>
                    </div>

                    <div class="form-group" style="text-align: center;">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة النفقة
                        </button>
                    </div>
                </form>
            </div>

            <!-- قائمة النفقات -->
            <div>
                <h4>سجل النفقات</h4>
                <div id="expenses-list" style="max-height: 300px; overflow-y: auto;">
                    <!-- سيتم ملء قائمة النفقات هنا -->
                </div>

                <div style="margin-top: 20px; padding: 15px; background: #e9ecef; border-radius: 8px;">
                    <div class="form-row">
                        <div style="flex: 1; text-align: center;">
                            <strong>إجمالي النفقات اليوم:</strong>
                            <span id="today-total" style="color: #dc3545; font-size: 1.2em;">0.00 DH</span>
                        </div>
                        <div style="flex: 1; text-align: center;">
                            <strong>إجمالي النفقات الشهر:</strong>
                            <span id="month-total" style="color: #fd7e14; font-size: 1.2em;">0.00 DH</span>
                        </div>
                        <div style="flex: 1; text-align: center;">
                            <strong>إجمالي النفقات:</strong>
                            <span id="total-expenses" style="color: #6f42c1; font-size: 1.2em;">0.00 DH</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group" style="text-align: center; margin-top: 30px;">
                <button type="button" class="btn btn-info" onclick="printExpensesReport()">
                    <i class="fas fa-print"></i> طباعة التقرير
                </button>
                <button type="button" class="btn" onclick="closeExpensesModal()" style="background: #6c757d; color: white; margin-right: 10px;">
                    <i class="fas fa-times"></i> إغلاق
                </button>
            </div>
        </div>
    </div>

    <!-- نافذة استيراد Excel -->
    <div id="import-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">استيراد التلاميذ من Excel</h3>
                <span class="close" onclick="closeImportModal()">&times;</span>
            </div>

            <div class="alert alert-warning">
                <strong>تنبيه:</strong> يجب أن يحتوي ملف Excel على الأعمدة التالية:
                <br>الاسم، المستوى، الفوج، الهاتف، الرسوم الشهرية
            </div>

            <form id="import-form">
                <div class="form-group">
                    <label for="excel-file">اختر ملف Excel</label>
                    <input type="file" id="excel-file" accept=".xlsx,.xls" required>
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="overwrite-existing"> استبدال البيانات الموجودة
                    </label>
                </div>

                <div class="form-group" style="text-align: center; margin-top: 30px;">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-upload"></i> استيراد البيانات
                    </button>
                    <button type="button" class="btn" onclick="closeImportModal()" style="background: #6c757d; color: white; margin-right: 10px;">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- منطقة الإشعارات -->
    <div id="notifications" style="position: fixed; top: 20px; left: 20px; z-index: 2000;"></div>

    <footer>
        <p>مؤسسة النور التربوي - الخيار الأمثل لتسيير مؤسسات التعليم الخاصة.</p>
    </footer>

    <!-- تضمين ملفات JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="auth.js"></script>
    <script src="loading.js"></script>
    <script src="notifications.js"></script>
    <script src="validation.js"></script>
    <script src="animations.js"></script>
    <script src="sync.js"></script>
    <script src="auto-logging.js"></script>
    <script src="receipt-printing.js"></script>
    <script src="script.js"></script>

    <script>
        // متغيرات عامة
        let students = [];
        let currentEditingStudent = null;
        let currentPaymentStudent = null;
        let nextStudentId = 1;

        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadStudentsData();
            loadLevelsAndGroups();
            setupEventListeners();
            updateStatistics();
            renderStudentsTable();
        });

        // تحميل بيانات التلاميذ
        function loadStudentsData() {
            try {
                const savedStudents = localStorage.getItem('sbea_students');
                if (savedStudents) {
                    students = JSON.parse(savedStudents);
                }

                const savedNextId = localStorage.getItem('sbea_next_student_id');
                if (savedNextId) {
                    nextStudentId = parseInt(savedNextId);
                } else {
                    // حساب الرقم التالي بناءً على أعلى رقم موجود
                    if (students.length > 0) {
                        const maxId = Math.max(...students.map(s => parseInt(s.barcode) || 0));
                        nextStudentId = maxId + 1;
                    }
                }
            } catch (error) {
                console.error('خطأ في تحميل بيانات التلاميذ:', error);
                students = [];
                nextStudentId = 1;
            }
        }

        // حفظ بيانات التلاميذ
        function saveStudentsData() {
            try {
                localStorage.setItem('sbea_students', JSON.stringify(students));
                localStorage.setItem('sbea_next_student_id', nextStudentId.toString());
                return true;
            } catch (error) {
                console.error('خطأ في حفظ البيانات:', error);
                showNotification('خطأ في حفظ البيانات', 'error');
                return false;
            }
        }

        // تحميل المستويات والأفواج
        function loadLevelsAndGroups() {
            const levels = [
                'التحضيري',
                'السنة الأولى ابتدائي',
                'السنة الثانية ابتدائي',
                'السنة الثالثة ابتدائي',
                'السنة الرابعة ابتدائي',
                'السنة الخامسة ابتدائي',
                'السنة السادسة ابتدائي',
                'السنة الأولى متوسط',
                'السنة الثانية متوسط',
                'السنة الثالثة متوسط',
                'السنة الرابعة متوسط',
                'السنة الأولى ثانوي',
                'السنة الثانية ثانوي',
                'السنة الثالثة ثانوي'
            ];

            const levelSelects = document.querySelectorAll('#student-level, #search-level');
            levelSelects.forEach(select => {
                if (select.id === 'search-level') {
                    select.innerHTML = '<option value="">جميع المستويات</option>';
                } else {
                    select.innerHTML = '<option value="">اختر المستوى</option>';
                }

                levels.forEach(level => {
                    const option = document.createElement('option');
                    option.value = level;
                    option.textContent = level;
                    select.appendChild(option);
                });
            });
        }

        // تحديث خيارات الأفواج
        function updateGroupOptions() {
            const level = document.getElementById('student-level').value;
            const groupSelect = document.getElementById('student-group');

            groupSelect.innerHTML = '<option value="">اختر الفوج</option>';

            if (level) {
                const groups = ['أ', 'ب', 'ج', 'د', 'هـ'];
                groups.forEach(group => {
                    const option = document.createElement('option');
                    option.value = group;
                    option.textContent = `فوج ${group}`;
                    groupSelect.appendChild(option);
                });
            }
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // البحث
            document.getElementById('search-name').addEventListener('input', filterStudents);
            document.getElementById('search-level').addEventListener('change', filterStudents);
            document.getElementById('search-group').addEventListener('change', filterStudents);
            document.getElementById('search-barcode').addEventListener('input', filterStudents);

            // نموذج إضافة/تعديل التلميذ
            document.getElementById('student-form').addEventListener('submit', handleStudentSubmit);

            // نموذج الدفع
            document.getElementById('payment-form').addEventListener('submit', handlePaymentSubmit);

            // نموذج الاستيراد
            document.getElementById('import-form').addEventListener('submit', handleImportSubmit);

            // نموذج النفقات
            document.getElementById('expense-form').addEventListener('submit', handleExpenseSubmit);

            // تحديث تاريخ الدفع إلى اليوم
            document.getElementById('payment-date').value = new Date().toISOString().split('T')[0];

            // تحديث تاريخ النفقة إلى اليوم
            document.getElementById('expense-date').value = new Date().toISOString().split('T')[0];
        }

        // عرض جدول التلاميذ
        function renderStudentsTable() {
            const tbody = document.getElementById('students-table-body');
            const emptyState = document.getElementById('empty-state');

            if (students.length === 0) {
                tbody.innerHTML = '';
                emptyState.style.display = 'block';
                return;
            }

            emptyState.style.display = 'none';

            let html = '';
            students.forEach(student => {
                const totalFee = (parseFloat(student.fee) || 0) + (parseFloat(student.transportFee) || 0);
                const paid = parseFloat(student.paid) || 0;
                const discount = parseFloat(student.discount) || 0;
                const remaining = totalFee - paid - discount;

                let paymentStatus = '';
                let statusClass = '';

                if (remaining <= 0) {
                    paymentStatus = 'مدفوع بالكامل';
                    statusClass = 'status-paid';
                } else if (paid > 0) {
                    paymentStatus = 'مدفوع جزئياً';
                    statusClass = 'status-partial';
                } else {
                    paymentStatus = 'غير مدفوع';
                    statusClass = 'status-unpaid';
                }

                html += `
                    <tr>
                        <td>
                            <img src="${student.picture || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjUwIiBoZWlnaHQ9IjUwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNSAyOUMyOC4zMTM3IDI5IDMxIDI2LjMxMzcgMzEgMjNDMzEgMTkuNjg2MyAyOC4zMTM3IDE3IDI1IDE3QzIxLjY4NjMgMTcgMTkgMTkuNjg2MyAxOSAyM0MxOSAyNi4zMTM3IDIxLjY4NjMgMjkgMjUgMjlaIiBmaWxsPSIjOUI5QkEwIi8+CjxwYXRoIGQ9Ik0xMyAzN0MxMyAzMi41ODE3IDE2LjU4MTcgMjkgMjEgMjlIMjlDMzMuNDE4MyAyOSAzNyAzMi41ODE3IDM3IDM3VjQxSDEzVjM3WiIgZmlsbD0iIzlCOUJBMCIvPgo8L3N2Zz4K'}"
                                 alt="صورة ${student.name}" class="student-photo">
                        </td>
                        <td><span class="student-barcode">${student.barcode}</span></td>
                        <td>${student.name}</td>
                        <td>${student.level}</td>
                        <td>${student.group}</td>
                        <td>${student.phone}</td>
                        <td>${totalFee.toFixed(2)} DH</td>
                        <td>${paid.toFixed(2)} DH</td>
                        <td>${remaining.toFixed(2)} DH</td>
                        <td><span class="payment-status ${statusClass}">${paymentStatus}</span></td>
                        <td>
                            <button class="action-btn btn-edit" onclick="editStudent('${student.id}')" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="action-btn btn-pay" onclick="openPaymentModal('${student.id}')" title="دفع">
                                <i class="fas fa-money-bill-wave"></i>
                            </button>
                            <button class="action-btn" onclick="openExpensesModal('${student.id}')" title="النفقات اليومية" style="background: #fd7e14; color: white;">
                                <i class="fas fa-receipt"></i>
                            </button>
                            <button class="action-btn btn-print" onclick="printStudentCard('${student.id}')" title="طباعة البطاقة">
                                <i class="fas fa-print"></i>
                            </button>
                            <button class="action-btn btn-delete" onclick="deleteStudent('${student.id}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // تحديث الإحصائيات
        function updateStatistics() {
            const totalStudents = students.length;
            let paidStudents = 0;
            let partialStudents = 0;
            let unpaidStudents = 0;
            let totalRevenue = 0;

            students.forEach(student => {
                const totalFee = (parseFloat(student.fee) || 0) + (parseFloat(student.transportFee) || 0);
                const paid = parseFloat(student.paid) || 0;
                const discount = parseFloat(student.discount) || 0;
                const remaining = totalFee - paid - discount;

                totalRevenue += paid;

                if (remaining <= 0) {
                    paidStudents++;
                } else if (paid > 0) {
                    partialStudents++;
                } else {
                    unpaidStudents++;
                }
            });

            document.getElementById('total-students').textContent = totalStudents;
            document.getElementById('paid-students').textContent = paidStudents;
            document.getElementById('partial-students').textContent = partialStudents;
            document.getElementById('unpaid-students').textContent = unpaidStudents;
            document.getElementById('total-revenue').textContent = totalRevenue.toFixed(2);
        }

        // فلترة التلاميذ
        function filterStudents() {
            const nameFilter = document.getElementById('search-name').value.toLowerCase();
            const levelFilter = document.getElementById('search-level').value;
            const groupFilter = document.getElementById('search-group').value;
            const barcodeFilter = document.getElementById('search-barcode').value;

            const filteredStudents = students.filter(student => {
                const nameMatch = !nameFilter || student.name.toLowerCase().includes(nameFilter);
                const levelMatch = !levelFilter || student.level === levelFilter;
                const groupMatch = !groupFilter || student.group === groupFilter;
                const barcodeMatch = !barcodeFilter || student.barcode.includes(barcodeFilter);

                return nameMatch && levelMatch && groupMatch && barcodeMatch;
            });

            renderFilteredStudents(filteredStudents);
        }

        // عرض التلاميذ المفلترين
        function renderFilteredStudents(filteredStudents) {
            const tbody = document.getElementById('students-table-body');
            const emptyState = document.getElementById('empty-state');

            if (filteredStudents.length === 0) {
                tbody.innerHTML = '<tr><td colspan="11" style="text-align: center; padding: 40px; color: #666;">لا توجد نتائج مطابقة للبحث</td></tr>';
                emptyState.style.display = 'none';
                return;
            }

            emptyState.style.display = 'none';

            let html = '';
            filteredStudents.forEach(student => {
                const totalFee = (parseFloat(student.fee) || 0) + (parseFloat(student.transportFee) || 0);
                const paid = parseFloat(student.paid) || 0;
                const discount = parseFloat(student.discount) || 0;
                const remaining = totalFee - paid - discount;

                let paymentStatus = '';
                let statusClass = '';

                if (remaining <= 0) {
                    paymentStatus = 'مدفوع بالكامل';
                    statusClass = 'status-paid';
                } else if (paid > 0) {
                    paymentStatus = 'مدفوع جزئياً';
                    statusClass = 'status-partial';
                } else {
                    paymentStatus = 'غير مدفوع';
                    statusClass = 'status-unpaid';
                }

                html += `
                    <tr>
                        <td>
                            <img src="${student.picture || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjUwIiBoZWlnaHQ9IjUwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNSAyOUMyOC4zMTM3IDI5IDMxIDI2LjMxMzcgMzEgMjNDMzEgMTkuNjg2MyAyOC4zMTM3IDE3IDI1IDE3QzIxLjY4NjMgMTcgMTkgMTkuNjg2MyAxOSAyM0MxOSAyNi4zMTM3IDIxLjY4NjMgMjkgMjUgMjlaIiBmaWxsPSIjOUI5QkEwIi8+CjxwYXRoIGQ9Ik0xMyAzN0MxMyAzMi41ODE3IDE2LjU4MTcgMjkgMjEgMjlIMjlDMzMuNDE4MyAyOSAzNyAzMi41ODE3IDM3IDM3VjQxSDEzVjM3WiIgZmlsbD0iIzlCOUJBMCIvPgo8L3N2Zz4K'}"
                                 alt="صورة ${student.name}" class="student-photo">
                        </td>
                        <td><span class="student-barcode">${student.barcode}</span></td>
                        <td>${student.name}</td>
                        <td>${student.level}</td>
                        <td>${student.group}</td>
                        <td>${student.phone}</td>
                        <td>${totalFee.toFixed(2)} DH</td>
                        <td>${paid.toFixed(2)} DH</td>
                        <td>${remaining.toFixed(2)} DH</td>
                        <td><span class="payment-status ${statusClass}">${paymentStatus}</span></td>
                        <td>
                            <button class="action-btn btn-edit" onclick="editStudent('${student.id}')" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="action-btn btn-pay" onclick="openPaymentModal('${student.id}')" title="دفع">
                                <i class="fas fa-money-bill-wave"></i>
                            </button>
                            <button class="action-btn btn-print" onclick="printStudentCard('${student.id}')" title="طباعة البطاقة">
                                <i class="fas fa-print"></i>
                            </button>
                            <button class="action-btn btn-delete" onclick="deleteStudent('${student.id}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // فتح نافذة إضافة تلميذ
        function openAddStudentModal() {
            currentEditingStudent = null;
            document.getElementById('modal-title').textContent = 'إضافة تلميذ جديد';
            document.getElementById('student-form').reset();
            document.getElementById('photo-preview').src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik01MCA1OEM1Ni42Mjc0IDU4IDYyIDUyLjYyNzQgNjIgNDZDNjIgMzkuMzcyNiA1Ni42Mjc0IDM0IDUwIDM0QzQzLjM3MjYgMzQgMzggMzkuMzcyNiAzOCA0NkMzOCA1Mi42Mjc0IDQzLjM3MjYgNTggNTAgNThaIiBmaWxsPSIjOUI5QkEwIi8+CjxwYXRoIGQ9Ik0yNiA3NEMyNiA2NS4xNjM0IDMzLjE2MzQgNTggNDIgNThINThDNjYuODM2NiA1OCA3NCA2NS4xNjM0IDc0IDc0VjgySDI2Vjc0WiIgZmlsbD0iIzlCOUJBMCIvPgo8L3N2Zz4K';
            document.getElementById('student-modal').style.display = 'block';
        }

        // إغلاق نافذة التلميذ
        function closeStudentModal() {
            document.getElementById('student-modal').style.display = 'none';
            currentEditingStudent = null;
        }

        // معاينة الصورة
        function previewPhoto(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('photo-preview').src = e.target.result;
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        // معالجة إرسال نموذج التلميذ
        function handleStudentSubmit(e) {
            e.preventDefault();

            const formData = new FormData();
            const photoFile = document.getElementById('student-photo').files[0];

            const studentData = {
                id: currentEditingStudent ? currentEditingStudent.id : 'student_' + Date.now(),
                name: document.getElementById('student-name').value.trim(),
                birthDate: document.getElementById('student-birth-date').value,
                level: document.getElementById('student-level').value,
                group: document.getElementById('student-group').value,
                phone: document.getElementById('student-phone').value.trim(),
                email: document.getElementById('student-email').value.trim(),
                address: document.getElementById('student-address').value.trim(),
                fee: parseFloat(document.getElementById('student-fee').value) || 0,
                transportFee: parseFloat(document.getElementById('student-transport').value) || 0,
                paid: parseFloat(document.getElementById('student-paid').value) || 0,
                discount: parseFloat(document.getElementById('student-discount').value) || 0,
                notes: document.getElementById('student-notes').value.trim(),
                picture: document.getElementById('photo-preview').src,
                createdAt: currentEditingStudent ? currentEditingStudent.createdAt : new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // إضافة الرقم التسلسلي للتلاميذ الجدد فقط
            if (!currentEditingStudent) {
                studentData.barcode = nextStudentId.toString().padStart(6, '0');
                nextStudentId++;
            } else {
                studentData.barcode = currentEditingStudent.barcode;
            }

            if (currentEditingStudent) {
                // تحديث تلميذ موجود
                const index = students.findIndex(s => s.id === currentEditingStudent.id);
                if (index !== -1) {
                    students[index] = studentData;
                    showNotification('تم تحديث بيانات التلميذ بنجاح', 'success');
                }
            } else {
                // إضافة تلميذ جديد
                students.push(studentData);
                showNotification('تم إضافة التلميذ بنجاح', 'success');
            }

            saveStudentsData();
            renderStudentsTable();
            updateStatistics();
            closeStudentModal();
        }

        // تعديل تلميذ
        function editStudent(studentId) {
            const student = students.find(s => s.id === studentId);
            if (!student) return;

            currentEditingStudent = student;
            document.getElementById('modal-title').textContent = 'تعديل بيانات التلميذ';

            // ملء النموذج بالبيانات
            document.getElementById('student-name').value = student.name || '';
            document.getElementById('student-birth-date').value = student.birthDate || '';
            document.getElementById('student-level').value = student.level || '';
            document.getElementById('student-group').value = student.group || '';
            document.getElementById('student-phone').value = student.phone || '';
            document.getElementById('student-email').value = student.email || '';
            document.getElementById('student-address').value = student.address || '';
            document.getElementById('student-fee').value = student.fee || 0;
            document.getElementById('student-transport').value = student.transportFee || 0;
            document.getElementById('student-paid').value = student.paid || 0;
            document.getElementById('student-discount').value = student.discount || 0;
            document.getElementById('student-notes').value = student.notes || '';
            document.getElementById('photo-preview').src = student.picture || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik01MCA1OEM1Ni42Mjc0IDU4IDYyIDUyLjYyNzQgNjIgNDZDNjIgMzkuMzcyNiA1Ni42Mjc0IDM0IDUwIDM0QzQzLjM3MjYgMzQgMzggMzkuMzcyNiAzOCA0NkMzOCA1Mi42Mjc0IDQzLjM3MjYgNTggNTAgNThaIiBmaWxsPSIjOUI5QkEwIi8+CjxwYXRoIGQ9Ik0yNiA3NEMyNiA2NS4xNjM0IDMzLjE2MzQgNTggNDIgNThINThDNjYuODM2NiA1OCA3NCA2NS4xNjM0IDc0IDc0VjgySDI2Vjc0WiIgZmlsbD0iIzlCOUJBMCIvPgo8L3N2Zz4K';

            // تحديث خيارات الأفواج
            updateGroupOptions();

            document.getElementById('student-modal').style.display = 'block';
        }

        // حذف تلميذ
        function deleteStudent(studentId) {
            const student = students.find(s => s.id === studentId);
            if (!student) return;

            if (confirm(`هل أنت متأكد من حذف التلميذ "${student.name}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
                students = students.filter(s => s.id !== studentId);
                saveStudentsData();
                renderStudentsTable();
                updateStatistics();
                showNotification('تم حذف التلميذ بنجاح', 'success');
            }
        }

        // فتح نافذة الدفع
        function openPaymentModal(studentId) {
            const student = students.find(s => s.id === studentId);
            if (!student) return;

            currentPaymentStudent = student;

            const totalFee = (parseFloat(student.fee) || 0) + (parseFloat(student.transportFee) || 0);
            const paid = parseFloat(student.paid) || 0;
            const discount = parseFloat(student.discount) || 0;
            const remaining = totalFee - paid - discount;

            document.getElementById('payment-student-info').innerHTML = `
                <strong>التلميذ:</strong> ${student.name}<br>
                <strong>الرقم التسلسلي:</strong> ${student.barcode}<br>
                <strong>إجمالي الرسوم:</strong> ${totalFee.toFixed(2)} DH<br>
                <strong>المدفوع:</strong> ${paid.toFixed(2)} DH<br>
                <strong>المتبقي:</strong> ${remaining.toFixed(2)} DH
            `;

            document.getElementById('payment-amount').value = remaining > 0 ? remaining.toFixed(2) : '';
            document.getElementById('payment-date').value = new Date().toISOString().split('T')[0];
            document.getElementById('payment-form').reset();
            document.getElementById('payment-amount').value = remaining > 0 ? remaining.toFixed(2) : '';

            document.getElementById('payment-modal').style.display = 'block';
        }

        // إغلاق نافذة الدفع
        function closePaymentModal() {
            document.getElementById('payment-modal').style.display = 'none';
            currentPaymentStudent = null;
        }

        // معالجة إرسال نموذج الدفع
        function handlePaymentSubmit(e) {
            e.preventDefault();

            if (!currentPaymentStudent) return;

            const paymentAmount = parseFloat(document.getElementById('payment-amount').value) || 0;
            const paymentMethod = document.getElementById('payment-method').value;
            const paymentDate = document.getElementById('payment-date').value;
            const paymentNotes = document.getElementById('payment-notes').value.trim();

            if (paymentAmount <= 0) {
                showNotification('يجب إدخال مبلغ صحيح', 'error');
                return;
            }

            // تحديث المبلغ المدفوع
            const studentIndex = students.findIndex(s => s.id === currentPaymentStudent.id);
            if (studentIndex !== -1) {
                const currentPaid = parseFloat(students[studentIndex].paid) || 0;
                students[studentIndex].paid = currentPaid + paymentAmount;
                students[studentIndex].updatedAt = new Date().toISOString();

                // إضافة سجل الدفعة
                if (!students[studentIndex].payments) {
                    students[studentIndex].payments = [];
                }

                students[studentIndex].payments.push({
                    id: 'payment_' + Date.now(),
                    amount: paymentAmount,
                    method: paymentMethod,
                    date: paymentDate,
                    notes: paymentNotes,
                    createdAt: new Date().toISOString()
                });

                saveStudentsData();
                renderStudentsTable();
                updateStatistics();
                closePaymentModal();

                showNotification(`تم تسجيل دفعة بمبلغ ${paymentAmount.toFixed(2)} DH للتلميذ ${currentPaymentStudent.name}`, 'success');
            }
        }

        // طباعة بطاقة التلميذ
        function printStudentCard(studentId) {
            const student = students.find(s => s.id === studentId);
            if (!student) return;

            const printWindow = window.open('', '_blank');
            const totalFee = (parseFloat(student.fee) || 0) + (parseFloat(student.transportFee) || 0);
            const paid = parseFloat(student.paid) || 0;
            const remaining = totalFee - paid;

            printWindow.document.write(`
                <!DOCTYPE html>
                <html dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>بطاقة التلميذ - ${student.name}</title>
                    <style>
                        body { font-family: 'Tajawal', Arial, sans-serif; margin: 20px; }
                        .card { border: 2px solid #333; padding: 20px; max-width: 400px; margin: 0 auto; }
                        .header { text-align: center; margin-bottom: 20px; }
                        .logo { width: 60px; height: 60px; }
                        .student-photo { width: 100px; height: 100px; border-radius: 50%; object-fit: cover; }
                        .info { margin: 10px 0; }
                        .barcode { font-family: monospace; font-size: 18px; font-weight: bold; text-align: center; margin: 15px 0; }
                    </style>
                </head>
                <body>
                    <div class="card">
                        <div class="header">
                            <img src="logo.png" alt="الشعار" class="logo">
                            <h2>مؤسسة النور التربوي</h2>
                            <h3>بطاقة التلميذ</h3>
                        </div>
                        <div style="text-align: center;">
                            <img src="${student.picture || ''}" alt="صورة التلميذ" class="student-photo">
                        </div>
                        <div class="barcode">${student.barcode}</div>
                        <div class="info"><strong>الاسم:</strong> ${student.name}</div>
                        <div class="info"><strong>المستوى:</strong> ${student.level}</div>
                        <div class="info"><strong>الفوج:</strong> ${student.group}</div>
                        <div class="info"><strong>الهاتف:</strong> ${student.phone}</div>
                        <div class="info"><strong>الرسوم الشهرية:</strong> ${totalFee.toFixed(2)} DH</div>
                        <div class="info"><strong>المدفوع:</strong> ${paid.toFixed(2)} DH</div>
                        <div class="info"><strong>المتبقي:</strong> ${remaining.toFixed(2)} DH</div>
                    </div>
                </body>
                </html>
            `);

            printWindow.document.close();
            printWindow.print();
        }

        // فتح نافذة الاستيراد
        function openImportModal() {
            document.getElementById('import-modal').style.display = 'block';
        }

        // إغلاق نافذة الاستيراد
        function closeImportModal() {
            document.getElementById('import-modal').style.display = 'none';
        }

        // معالجة استيراد Excel
        function handleImportSubmit(e) {
            e.preventDefault();

            const fileInput = document.getElementById('excel-file');
            const file = fileInput.files[0];
            const overwriteExisting = document.getElementById('overwrite-existing').checked;

            if (!file) {
                showNotification('يرجى اختيار ملف Excel', 'error');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    const firstSheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[firstSheetName];
                    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                    if (jsonData.length < 2) {
                        showNotification('ملف Excel فارغ أو لا يحتوي على بيانات صحيحة', 'error');
                        return;
                    }

                    // التحقق من الأعمدة المطلوبة
                    const headers = jsonData[0];
                    const requiredColumns = ['الاسم', 'المستوى', 'الفوج', 'الهاتف', 'الرسوم الشهرية'];
                    const missingColumns = requiredColumns.filter(col => !headers.includes(col));

                    if (missingColumns.length > 0) {
                        showNotification(`الأعمدة التالية مفقودة: ${missingColumns.join(', ')}`, 'error');
                        return;
                    }

                    // معالجة البيانات
                    const importedStudents = [];
                    let successCount = 0;
                    let errorCount = 0;

                    for (let i = 1; i < jsonData.length; i++) {
                        const row = jsonData[i];
                        if (!row || row.length === 0) continue;

                        try {
                            const studentData = {
                                id: 'student_' + Date.now() + '_' + i,
                                name: row[headers.indexOf('الاسم')] || '',
                                level: row[headers.indexOf('المستوى')] || '',
                                group: row[headers.indexOf('الفوج')] || '',
                                phone: row[headers.indexOf('الهاتف')] || '',
                                fee: parseFloat(row[headers.indexOf('الرسوم الشهرية')]) || 0,
                                email: row[headers.indexOf('البريد الإلكتروني')] || '',
                                address: row[headers.indexOf('العنوان')] || '',
                                transportFee: parseFloat(row[headers.indexOf('رسوم النقل')]) || 0,
                                paid: parseFloat(row[headers.indexOf('المدفوع')]) || 0,
                                discount: parseFloat(row[headers.indexOf('الخصم')]) || 0,
                                notes: row[headers.indexOf('ملاحظات')] || '',
                                barcode: nextStudentId.toString().padStart(6, '0'),
                                picture: '',
                                createdAt: new Date().toISOString(),
                                updatedAt: new Date().toISOString(),
                                payments: []
                            };

                            // التحقق من صحة البيانات الأساسية
                            if (!studentData.name || !studentData.level || !studentData.group || !studentData.phone) {
                                errorCount++;
                                continue;
                            }

                            importedStudents.push(studentData);
                            nextStudentId++;
                            successCount++;

                        } catch (error) {
                            console.error('خطأ في معالجة الصف:', i, error);
                            errorCount++;
                        }
                    }

                    if (importedStudents.length === 0) {
                        showNotification('لم يتم استيراد أي تلميذ. تحقق من صحة البيانات', 'error');
                        return;
                    }

                    // إضافة التلاميذ إلى القائمة
                    if (overwriteExisting) {
                        students = importedStudents;
                    } else {
                        students = students.concat(importedStudents);
                    }

                    saveStudentsData();
                    renderStudentsTable();
                    updateStatistics();
                    closeImportModal();

                    showNotification(
                        `تم استيراد ${successCount} تلميذ بنجاح${errorCount > 0 ? ` (${errorCount} أخطاء)` : ''}`,
                        'success'
                    );

                } catch (error) {
                    console.error('خطأ في قراءة ملف Excel:', error);
                    showNotification('خطأ في قراءة ملف Excel. تأكد من صحة الملف', 'error');
                }
            };
            reader.readAsArrayBuffer(file);
        }

        // تحميل نموذج Excel
        function downloadExcelTemplate() {
            const headers = [
                'الاسم',
                'المستوى',
                'الفوج',
                'الهاتف',
                'الرسوم الشهرية',
                'البريد الإلكتروني',
                'العنوان',
                'رسوم النقل',
                'المدفوع',
                'الخصم',
                'ملاحظات'
            ];

            const sampleData = [
                [
                    'أحمد محمد علي',
                    'السنة الأولى ابتدائي',
                    'أ',
                    '0612345678',
                    '500',
                    '<EMAIL>',
                    'الدار البيضاء',
                    '100',
                    '0',
                    '0',
                    'تلميذ مجتهد'
                ],
                [
                    'فاطمة حسن',
                    'السنة الثانية ابتدائي',
                    'ب',
                    '0623456789',
                    '550',
                    '<EMAIL>',
                    'الرباط',
                    '0',
                    '200',
                    '50',
                    ''
                ]
            ];

            const ws = XLSX.utils.aoa_to_sheet([headers, ...sampleData]);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'التلاميذ');

            // تنسيق الأعمدة
            const colWidths = [
                { wch: 20 }, // الاسم
                { wch: 25 }, // المستوى
                { wch: 10 }, // الفوج
                { wch: 15 }, // الهاتف
                { wch: 15 }, // الرسوم الشهرية
                { wch: 25 }, // البريد الإلكتروني
                { wch: 20 }, // العنوان
                { wch: 12 }, // رسوم النقل
                { wch: 12 }, // المدفوع
                { wch: 10 }, // الخصم
                { wch: 20 }  // ملاحظات
            ];
            ws['!cols'] = colWidths;

            XLSX.writeFile(wb, `نموذج_استيراد_التلاميذ_${new Date().toISOString().split('T')[0]}.xlsx`);
            showNotification('تم تحميل نموذج Excel بنجاح', 'success');
        }

        // فتح نافذة النفقات اليومية
        function openExpensesModal(studentId) {
            const student = students.find(s => s.id === studentId);
            if (!student) return;

            currentExpenseStudent = student;

            document.getElementById('expenses-student-info').innerHTML = `
                <strong>التلميذ:</strong> ${student.name}<br>
                <strong>الرقم التسلسلي:</strong> ${student.barcode}<br>
                <strong>المستوى:</strong> ${student.level} - فوج ${student.group}
            `;

            // تهيئة النفقات إذا لم تكن موجودة
            if (!student.expenses) {
                student.expenses = [];
            }

            document.getElementById('expense-form').reset();
            document.getElementById('expense-date').value = new Date().toISOString().split('T')[0];

            renderExpensesList();
            updateExpensesTotals();

            document.getElementById('expenses-modal').style.display = 'block';
        }

        // إغلاق نافذة النفقات
        function closeExpensesModal() {
            document.getElementById('expenses-modal').style.display = 'none';
            currentExpenseStudent = null;
        }

        // معالجة إضافة نفقة جديدة
        function handleExpenseSubmit(e) {
            e.preventDefault();

            if (!currentExpenseStudent) return;

            const expenseData = {
                id: 'expense_' + Date.now(),
                type: document.getElementById('expense-type').value,
                amount: parseFloat(document.getElementById('expense-amount').value) || 0,
                date: document.getElementById('expense-date').value,
                paidBy: document.getElementById('expense-paid-by').value,
                description: document.getElementById('expense-description').value.trim(),
                createdAt: new Date().toISOString()
            };

            if (expenseData.amount <= 0) {
                showNotification('يجب إدخال مبلغ صحيح', 'error');
                return;
            }

            // إضافة النفقة إلى التلميذ
            const studentIndex = students.findIndex(s => s.id === currentExpenseStudent.id);
            if (studentIndex !== -1) {
                if (!students[studentIndex].expenses) {
                    students[studentIndex].expenses = [];
                }

                students[studentIndex].expenses.push(expenseData);
                students[studentIndex].updatedAt = new Date().toISOString();

                saveStudentsData();
                renderExpensesList();
                updateExpensesTotals();

                document.getElementById('expense-form').reset();
                document.getElementById('expense-date').value = new Date().toISOString().split('T')[0];

                showNotification(`تم إضافة نفقة بمبلغ ${expenseData.amount.toFixed(2)} DH`, 'success');
            }
        }

        // عرض قائمة النفقات
        function renderExpensesList() {
            if (!currentExpenseStudent || !currentExpenseStudent.expenses) {
                document.getElementById('expenses-list').innerHTML = '<p style="text-align: center; color: #666;">لا توجد نفقات مسجلة</p>';
                return;
            }

            const expenses = currentExpenseStudent.expenses.sort((a, b) => new Date(b.date) - new Date(a.date));

            let html = '<div style="display: flex; flex-direction: column; gap: 10px;">';

            expenses.forEach(expense => {
                const expenseTypeNames = {
                    'meals': 'وجبات',
                    'transport': 'نقل',
                    'books': 'كتب ومستلزمات',
                    'activities': 'أنشطة',
                    'medical': 'طبية',
                    'uniform': 'زي مدرسي',
                    'other': 'أخرى'
                };

                const paidByNames = {
                    'student': 'التلميذ',
                    'parent': 'ولي الأمر',
                    'school': 'المدرسة'
                };

                html += `
                    <div style="border: 1px solid #e1e5e9; border-radius: 8px; padding: 15px; background: white;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <div>
                                <strong style="color: #667eea;">${expenseTypeNames[expense.type] || expense.type}</strong>
                                <span style="margin-right: 10px; color: #28a745; font-weight: bold;">${expense.amount.toFixed(2)} DH</span>
                            </div>
                            <div style="display: flex; gap: 5px;">
                                <button onclick="deleteExpense('${expense.id}')" class="action-btn btn-delete" style="padding: 5px 8px; font-size: 12px;" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div style="font-size: 0.9em; color: #666;">
                            <div><strong>التاريخ:</strong> ${new Date(expense.date).toLocaleDateString('ar-MA')}</div>
                            <div><strong>دفع بواسطة:</strong> ${paidByNames[expense.paidBy] || expense.paidBy}</div>
                            ${expense.description ? `<div><strong>الوصف:</strong> ${expense.description}</div>` : ''}
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            document.getElementById('expenses-list').innerHTML = html;
        }

        // تحديث إجماليات النفقات
        function updateExpensesTotals() {
            if (!currentExpenseStudent || !currentExpenseStudent.expenses) {
                document.getElementById('today-total').textContent = '0.00 DH';
                document.getElementById('month-total').textContent = '0.00 DH';
                document.getElementById('total-expenses').textContent = '0.00 DH';
                return;
            }

            const today = new Date().toISOString().split('T')[0];
            const currentMonth = new Date().toISOString().substring(0, 7); // YYYY-MM

            let todayTotal = 0;
            let monthTotal = 0;
            let totalExpenses = 0;

            currentExpenseStudent.expenses.forEach(expense => {
                const amount = parseFloat(expense.amount) || 0;
                totalExpenses += amount;

                if (expense.date === today) {
                    todayTotal += amount;
                }

                if (expense.date.startsWith(currentMonth)) {
                    monthTotal += amount;
                }
            });

            document.getElementById('today-total').textContent = todayTotal.toFixed(2) + ' DH';
            document.getElementById('month-total').textContent = monthTotal.toFixed(2) + ' DH';
            document.getElementById('total-expenses').textContent = totalExpenses.toFixed(2) + ' DH';
        }

        // حذف نفقة
        function deleteExpense(expenseId) {
            if (!currentExpenseStudent || !confirm('هل أنت متأكد من حذف هذه النفقة؟')) return;

            const studentIndex = students.findIndex(s => s.id === currentExpenseStudent.id);
            if (studentIndex !== -1) {
                students[studentIndex].expenses = students[studentIndex].expenses.filter(e => e.id !== expenseId);
                students[studentIndex].updatedAt = new Date().toISOString();

                saveStudentsData();
                renderExpensesList();
                updateExpensesTotals();

                showNotification('تم حذف النفقة بنجاح', 'success');
            }
        }

        // طباعة تقرير النفقات
        function printExpensesReport() {
            if (!currentExpenseStudent) return;

            const printWindow = window.open('', '_blank');
            const expenses = currentExpenseStudent.expenses || [];

            let expensesHTML = '';
            let totalAmount = 0;

            expenses.sort((a, b) => new Date(b.date) - new Date(a.date)).forEach(expense => {
                const expenseTypeNames = {
                    'meals': 'وجبات',
                    'transport': 'نقل',
                    'books': 'كتب ومستلزمات',
                    'activities': 'أنشطة',
                    'medical': 'طبية',
                    'uniform': 'زي مدرسي',
                    'other': 'أخرى'
                };

                const paidByNames = {
                    'student': 'التلميذ',
                    'parent': 'ولي الأمر',
                    'school': 'المدرسة'
                };

                totalAmount += parseFloat(expense.amount) || 0;

                expensesHTML += `
                    <tr>
                        <td>${new Date(expense.date).toLocaleDateString('ar-MA')}</td>
                        <td>${expenseTypeNames[expense.type] || expense.type}</td>
                        <td>${expense.amount.toFixed(2)} DH</td>
                        <td>${paidByNames[expense.paidBy] || expense.paidBy}</td>
                        <td>${expense.description || '-'}</td>
                    </tr>
                `;
            });

            printWindow.document.write(`
                <!DOCTYPE html>
                <html dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>تقرير النفقات اليومية - ${currentExpenseStudent.name}</title>
                    <style>
                        body { font-family: 'Tajawal', Arial, sans-serif; margin: 20px; }
                        .header { text-align: center; margin-bottom: 30px; }
                        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                        th { background-color: #f8f9fa; }
                        .total { background-color: #e9ecef; font-weight: bold; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>مؤسسة النور التربوي للتعليم الخصوصي</h1>
                        <h2>تقرير النفقات اليومية</h2>
                        <p><strong>التلميذ:</strong> ${currentExpenseStudent.name}</p>
                        <p><strong>الرقم التسلسلي:</strong> ${currentExpenseStudent.barcode}</p>
                        <p><strong>المستوى:</strong> ${currentExpenseStudent.level} - فوج ${currentExpenseStudent.group}</p>
                        <p><strong>تاريخ التقرير:</strong> ${new Date().toLocaleDateString('ar-MA')}</p>
                    </div>

                    <table>
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>نوع النفقة</th>
                                <th>المبلغ</th>
                                <th>دفع بواسطة</th>
                                <th>الوصف</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${expensesHTML}
                            <tr class="total">
                                <td colspan="2">الإجمالي</td>
                                <td>${totalAmount.toFixed(2)} DH</td>
                                <td colspan="2">-</td>
                            </tr>
                        </tbody>
                    </table>
                </body>
                </html>
            `);

            printWindow.document.close();
            printWindow.print();
        }

        // متغير للتلميذ الحالي في نافذة النفقات
        let currentExpenseStudent = null;

        // تصدير البيانات
        function exportStudents() {
            const csvContent = generateCSV();
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `students_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showNotification('تم تصدير البيانات بنجاح', 'success');
        }

        // إنشاء ملف CSV
        function generateCSV() {
            const headers = ['الرقم التسلسلي', 'الاسم', 'المستوى', 'الفوج', 'الهاتف', 'الرسوم الشهرية', 'رسوم النقل', 'المدفوع', 'الخصم', 'المتبقي'];
            let csv = headers.join(',') + '\n';

            students.forEach(student => {
                const totalFee = (parseFloat(student.fee) || 0) + (parseFloat(student.transportFee) || 0);
                const paid = parseFloat(student.paid) || 0;
                const discount = parseFloat(student.discount) || 0;
                const remaining = totalFee - paid - discount;

                const row = [
                    student.barcode,
                    `"${student.name}"`,
                    `"${student.level}"`,
                    `"${student.group}"`,
                    student.phone,
                    student.fee || 0,
                    student.transportFee || 0,
                    paid,
                    discount,
                    remaining.toFixed(2)
                ];
                csv += row.join(',') + '\n';
            });

            return csv;
        }

        // طباعة قائمة التلاميذ
        function printStudentsList() {
            const printWindow = window.open('', '_blank');

            let tableHTML = `
                <table border="1" style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background-color: #f8f9fa;">
                            <th>الرقم التسلسلي</th>
                            <th>الاسم</th>
                            <th>المستوى</th>
                            <th>الفوج</th>
                            <th>الهاتف</th>
                            <th>الرسوم</th>
                            <th>المدفوع</th>
                            <th>المتبقي</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            students.forEach(student => {
                const totalFee = (parseFloat(student.fee) || 0) + (parseFloat(student.transportFee) || 0);
                const paid = parseFloat(student.paid) || 0;
                const discount = parseFloat(student.discount) || 0;
                const remaining = totalFee - paid - discount;

                tableHTML += `
                    <tr>
                        <td style="text-align: center;">${student.barcode}</td>
                        <td>${student.name}</td>
                        <td>${student.level}</td>
                        <td>${student.group}</td>
                        <td>${student.phone}</td>
                        <td style="text-align: center;">${totalFee.toFixed(2)} DH</td>
                        <td style="text-align: center;">${paid.toFixed(2)} DH</td>
                        <td style="text-align: center;">${remaining.toFixed(2)} DH</td>
                    </tr>
                `;
            });

            tableHTML += '</tbody></table>';

            printWindow.document.write(`
                <!DOCTYPE html>
                <html dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>قائمة التلاميذ</title>
                    <style>
                        body { font-family: 'Tajawal', Arial, sans-serif; margin: 20px; }
                        .header { text-align: center; margin-bottom: 30px; }
                        table { font-size: 12px; }
                        th, td { padding: 8px; text-align: right; }
                        th { background-color: #f8f9fa !important; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>مؤسسة النور التربوي للتعليم الخصوصي</h1>
                        <h2>قائمة التلاميذ</h2>
                        <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-MA')}</p>
                        <p>إجمالي التلاميذ: ${students.length}</p>
                    </div>
                    ${tableHTML}
                </body>
                </html>
            `);

            printWindow.document.close();
            printWindow.print();
        }

        // عرض الإشعارات
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type}`;
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                left: 20px;
                z-index: 2000;
                min-width: 300px;
                animation: slideIn 0.3s ease;
            `;

            document.getElementById('notifications').appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // إغلاق النوافذ عند النقر خارجها
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        };

        // تحديث خيارات البحث بالأفواج
        function updateSearchGroups() {
            const searchGroupSelect = document.getElementById('search-group');
            const uniqueGroups = [...new Set(students.map(s => s.group).filter(g => g))];

            searchGroupSelect.innerHTML = '<option value="">جميع الأفواج</option>';
            uniqueGroups.forEach(group => {
                const option = document.createElement('option');
                option.value = group;
                option.textContent = `فوج ${group}`;
                searchGroupSelect.appendChild(option);
            });
        }

        // إضافة أنماط CSS للرسوم المتحركة
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(-100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(-100%); opacity: 0; }
            }
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(20px); }
                to { opacity: 1; transform: translateY(0); }
            }
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }
            @keyframes bounce {
                0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
                40%, 43% { transform: translateY(-10px); }
                70% { transform: translateY(-5px); }
                90% { transform: translateY(-2px); }
            }
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
                20%, 40%, 60%, 80% { transform: translateX(5px); }
            }
            @keyframes glow {
                0% { box-shadow: 0 0 5px rgba(102, 126, 234, 0.5); }
                50% { box-shadow: 0 0 20px rgba(102, 126, 234, 0.8); }
                100% { box-shadow: 0 0 5px rgba(102, 126, 234, 0.5); }
            }

            /* تأثيرات إضافية للعناصر */
            .animate-on-load {
                animation: fadeIn 0.6s ease-out;
            }

            .hover-pulse:hover {
                animation: pulse 0.6s ease-in-out;
            }

            .hover-bounce:hover {
                animation: bounce 0.8s ease-in-out;
            }

            .error-shake {
                animation: shake 0.5s ease-in-out;
            }

            .success-glow {
                animation: glow 1s ease-in-out;
            }

            /* تأثيرات الانتقال للجداول */
            .students-table tbody tr {
                transition: all 0.3s ease;
            }

            .students-table tbody tr:hover {
                background-color: #f8f9fa !important;
                transform: translateX(5px);
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }

            /* تأثيرات البطاقات */
            .stat-card {
                transition: all 0.3s ease;
            }

            .stat-card:hover {
                transform: translateY(-8px) scale(1.02);
                box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            }

            /* تأثيرات الأزرار */
            .btn {
                position: relative;
                overflow: hidden;
                transition: all 0.3s ease;
            }

            .btn::before {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                width: 0;
                height: 0;
                background: rgba(255,255,255,0.2);
                border-radius: 50%;
                transform: translate(-50%, -50%);
                transition: width 0.6s, height 0.6s;
            }

            .btn:active::before {
                width: 300px;
                height: 300px;
            }

            /* تأثيرات النوافذ المنبثقة */
            .modal {
                backdrop-filter: blur(8px);
                transition: all 0.3s ease;
            }

            .modal-content {
                animation: modalSlideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            }

            @keyframes modalSlideIn {
                from {
                    opacity: 0;
                    transform: translateY(-100px) scale(0.8);
                }
                to {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
            }

            /* تأثيرات التحميل */
            .loading-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(255,255,255,0.9);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
                backdrop-filter: blur(5px);
            }

            .loading-spinner {
                width: 60px;
                height: 60px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #667eea;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            /* تأثيرات الإشعارات */
            .notification-enter {
                animation: slideInRight 0.5s ease-out;
            }

            .notification-exit {
                animation: slideOutRight 0.3s ease-in;
            }

            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // إضافة تأثيرات التحميل للعناصر
        function addLoadingAnimations() {
            const elements = document.querySelectorAll('.stat-card, .students-controls, .students-table-container');
            elements.forEach((element, index) => {
                element.style.animationDelay = `${index * 0.1}s`;
                element.classList.add('animate-on-load');
            });
        }

        // إضافة تأثيرات التفاعل للأزرار
        function addButtonEffects() {
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.classList.add('hover-pulse');

                button.addEventListener('click', function(e) {
                    // تأثير الموجة عند النقر
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.cssText = `
                        position: absolute;
                        width: ${size}px;
                        height: ${size}px;
                        left: ${x}px;
                        top: ${y}px;
                        background: rgba(255,255,255,0.4);
                        border-radius: 50%;
                        transform: scale(0);
                        animation: ripple 0.6s linear;
                        pointer-events: none;
                    `;

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        }

        // إضافة تأثير الريبل
        const rippleStyle = document.createElement('style');
        rippleStyle.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(rippleStyle);

        // تطبيق التأثيرات عند تحميل الصفحة
        setTimeout(() => {
            addLoadingAnimations();
            addButtonEffects();
        }, 100);
    </script>
</body>
</html>
