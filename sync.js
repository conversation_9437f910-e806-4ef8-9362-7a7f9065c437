// ===================================================================
//                    نظام المزامنة الفورية
// ===================================================================

class RealTimeSync {
    constructor() {
        this.syncChannel = new BroadcastChannel('sbea_sync');
        this.isOnline = navigator.onLine;
        this.syncQueue = [];
        this.lastSyncTime = Date.now();
        this.syncInterval = null;
        this.conflictResolution = 'latest_wins'; // أو 'manual'
        
        this.initializeSync();
    }

    // تهيئة نظام المزامنة
    initializeSync() {
        // مستمع للرسائل من النوافذ الأخرى
        this.syncChannel.addEventListener('message', (event) => {
            this.handleSyncMessage(event.data);
        });

        // مستمع لحالة الاتصال
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.processSyncQueue();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
        });

        // مستمع لتغييرات التخزين المحلي
        window.addEventListener('storage', (event) => {
            this.handleStorageChange(event);
        });

        // مزامنة دورية كل 5 ثوانٍ
        this.syncInterval = setInterval(() => {
            this.performPeriodicSync();
        }, 5000);

        // تنظيف عند إغلاق النافذة
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });

        console.log('تم تهيئة نظام المزامنة الفورية');
    }

    // معالجة رسائل المزامنة
    handleSyncMessage(data) {
        const currentUser = window.authSystem?.getCurrentUser();
        if (!currentUser) return;

        switch (data.type) {
            case 'data_update':
                this.handleDataUpdate(data);
                break;
            case 'user_activity':
                this.handleUserActivity(data);
                break;
            case 'conflict_detected':
                this.handleConflict(data);
                break;
            case 'sync_request':
                this.handleSyncRequest(data);
                break;
        }
    }

    // معالجة تحديث البيانات
    handleDataUpdate(data) {
        const { key, newData, timestamp, userId, action } = data;
        
        // تجاهل التحديثات من نفس المستخدم
        const currentUser = window.authSystem?.getCurrentUser();
        if (currentUser && currentUser.userId === userId) return;

        // التحقق من الطابع الزمني لتجنب التحديثات القديمة
        const lastUpdate = localStorage.getItem(`${key}_last_update`);
        if (lastUpdate && parseInt(lastUpdate) >= timestamp) return;

        try {
            // تحديث البيانات
            localStorage.setItem(key, JSON.stringify(newData));
            localStorage.setItem(`${key}_last_update`, timestamp.toString());

            // إشعار المستخدم بالتحديث
            this.showUpdateNotification(action, userId);

            // تحديث الواجهة
            this.refreshUI(key);

            // تسجيل النشاط
            this.logSyncActivity('data_received', `تم استقبال تحديث ${key} من ${userId}`);

        } catch (error) {
            console.error('خطأ في معالجة تحديث البيانات:', error);
        }
    }

    // معالجة نشاط المستخدم
    handleUserActivity(data) {
        const { userId, action, description, timestamp } = data;
        
        // إضافة النشاط لسجل الأنشطة
        const activities = JSON.parse(localStorage.getItem('sbea_user_activities') || '[]');
        activities.unshift({
            id: `sync_${Date.now()}`,
            userId,
            action,
            description,
            timestamp,
            source: 'sync'
        });

        // الاحتفاظ بآخر 1000 نشاط
        if (activities.length > 1000) {
            activities.splice(1000);
        }

        localStorage.setItem('sbea_user_activities', JSON.stringify(activities));

        // إشعار في الوقت الفعلي
        this.showActivityNotification(description);
    }

    // معالجة تضارب البيانات
    handleConflict(data) {
        const { key, localData, remoteData, timestamp } = data;
        
        if (this.conflictResolution === 'latest_wins') {
            // الأحدث يفوز
            const localTimestamp = localStorage.getItem(`${key}_last_update`) || 0;
            if (timestamp > parseInt(localTimestamp)) {
                localStorage.setItem(key, JSON.stringify(remoteData));
                localStorage.setItem(`${key}_last_update`, timestamp.toString());
                this.refreshUI(key);
            }
        } else {
            // حل يدوي للتضارب
            this.showConflictResolutionDialog(key, localData, remoteData);
        }
    }

    // إرسال تحديث للنوافذ الأخرى
    broadcastUpdate(key, data, action = 'update') {
        const currentUser = window.authSystem?.getCurrentUser();
        if (!currentUser) return;

        const message = {
            type: 'data_update',
            key,
            newData: data,
            timestamp: Date.now(),
            userId: currentUser.userId,
            userName: currentUser.name,
            action,
            sessionId: currentUser.sessionId
        };

        this.syncChannel.postMessage(message);
        
        // حفظ الطابع الزمني
        localStorage.setItem(`${key}_last_update`, message.timestamp.toString());

        this.logSyncActivity('data_sent', `تم إرسال تحديث ${key}`);
    }

    // إرسال نشاط المستخدم
    broadcastActivity(action, description) {
        const currentUser = window.authSystem?.getCurrentUser();
        if (!currentUser) return;

        const message = {
            type: 'user_activity',
            userId: currentUser.userId,
            userName: currentUser.name,
            action,
            description,
            timestamp: new Date().toISOString()
        };

        this.syncChannel.postMessage(message);
    }

    // مزامنة دورية
    performPeriodicSync() {
        if (!this.isOnline) return;

        // طلب مزامنة من النوافذ الأخرى
        this.syncChannel.postMessage({
            type: 'sync_request',
            timestamp: Date.now()
        });
    }

    // معالجة طلب المزامنة
    handleSyncRequest(data) {
        // إرسال آخر التحديثات
        const keys = ['sbea_students', 'sbea_teachers', 'sbea_staff', 'sbea_groups'];
        
        keys.forEach(key => {
            const data = localStorage.getItem(key);
            const lastUpdate = localStorage.getItem(`${key}_last_update`);
            
            if (data && lastUpdate) {
                this.broadcastUpdate(key, JSON.parse(data), 'sync');
            }
        });
    }

    // عرض إشعار التحديث
    showUpdateNotification(action, userId) {
        const notification = document.createElement('div');
        notification.className = 'sync-notification update';
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-sync-alt"></i>
                <span>تم تحديث البيانات بواسطة مستخدم آخر</span>
                <button onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // إزالة تلقائية بعد 5 ثوانٍ
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    // عرض إشعار النشاط
    showActivityNotification(description) {
        const notification = document.createElement('div');
        notification.className = 'sync-notification activity';
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-bell"></i>
                <span>${description}</span>
                <button onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 3000);
    }

    // تحديث الواجهة
    refreshUI(key) {
        try {
            // تحديث الجداول والقوائم حسب نوع البيانات
            switch (key) {
                case 'sbea_students':
                    if (typeof renderStudents === 'function') {
                        renderStudents();
                    }
                    // تحديث الصفحة الجديدة للطلاب
                    if (typeof renderStudentsTable === 'function') {
                        renderStudentsTable();
                    }
                    if (typeof updateStatistics === 'function') {
                        updateStatistics();
                    }
                    if (typeof updateStudentAndTeacherSelects === 'function') {
                        updateStudentAndTeacherSelects();
                    }
                    if (typeof updateAllSelects === 'function') {
                        updateAllSelects();
                    }
                    break;

                case 'sbea_teachers':
                    if (typeof displayTeachers === 'function') {
                        displayTeachers();
                    }
                    if (typeof updateAllSelects === 'function') {
                        updateAllSelects();
                    }
                    break;

                case 'sbea_groups':
                    if (typeof displayGroups === 'function') {
                        displayGroups();
                    }
                    if (typeof updateAllSelects === 'function') {
                        updateAllSelects();
                    }
                    break;

                case 'sbea_staff':
                    if (typeof displayStaff === 'function') {
                        displayStaff();
                    }
                    break;

                case 'sbea_users':
                    if (typeof loadUsers === 'function') {
                        loadUsers();
                    }
                    if (typeof updateUsersStats === 'function') {
                        updateUsersStats();
                    }
                    break;

                case 'sbea_payments':
                    if (typeof loadPayments === 'function') {
                        loadPayments();
                    }
                    break;
            }

            // تحديث الإحصائيات
            if (typeof updateDashboardStats === 'function') {
                updateDashboardStats();
            }

            // تحديث واجهة المستخدم
            if (window.authSystem && typeof window.authSystem.updateUserInterface === 'function') {
                window.authSystem.updateUserInterface();
            }

        } catch (error) {
            console.error('خطأ في تحديث الواجهة:', error);
        }
    }

    // دالة التحقق من حالة الاتصال
    checkConnectionStatus() {
        const wasOnline = this.isOnline;
        this.isOnline = navigator.onLine;

        if (!wasOnline && this.isOnline) {
            // عاد الاتصال
            this.showConnectionNotification('تم استعادة الاتصال', 'success');
            this.processSyncQueue();
        } else if (wasOnline && !this.isOnline) {
            // انقطع الاتصال
            this.showConnectionNotification('انقطع الاتصال - سيتم الحفظ محلياً', 'warning');
        }
    }

    // عرض إشعار حالة الاتصال
    showConnectionNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `sync-notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${type === 'success' ? 'wifi' : 'exclamation-triangle'}"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    // معالجة قائمة انتظار المزامنة
    processSyncQueue() {
        if (!this.isOnline || this.syncQueue.length === 0) return;

        const queueCopy = [...this.syncQueue];
        this.syncQueue = [];

        queueCopy.forEach(item => {
            this.broadcastUpdate(item.key, item.data, item.action);
        });

        console.log(`تم معالجة ${queueCopy.length} عنصر من قائمة انتظار المزامنة`);
    }

    // إضافة عنصر لقائمة انتظار المزامنة
    addToSyncQueue(key, data, action) {
        this.syncQueue.push({
            key,
            data,
            action,
            timestamp: Date.now()
        });

        // تنظيف القائمة من العناصر القديمة (أكثر من ساعة)
        const oneHourAgo = Date.now() - (60 * 60 * 1000);
        this.syncQueue = this.syncQueue.filter(item => item.timestamp > oneHourAgo);
    }

    // تسجيل أنشطة المزامنة
    logSyncActivity(action, description) {
        const syncLogs = JSON.parse(localStorage.getItem('sbea_sync_logs') || '[]');
        
        syncLogs.unshift({
            id: `sync_log_${Date.now()}`,
            action,
            description,
            timestamp: new Date().toISOString(),
            user: window.authSystem?.getCurrentUser()?.name || 'غير معروف'
        });

        // الاحتفاظ بآخر 500 سجل
        if (syncLogs.length > 500) {
            syncLogs.splice(500);
        }

        localStorage.setItem('sbea_sync_logs', JSON.stringify(syncLogs));
    }

    // تنظيف الموارد
    cleanup() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
        }
        this.syncChannel.close();
    }
}

// تهيئة نظام المزامنة
let syncSystem = null;

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة نظام المزامنة فقط إذا كان المستخدم مسجل دخول
    if (window.authSystem?.isLoggedIn()) {
        syncSystem = new RealTimeSync();
        
        // ربط نظام المزامنة بدوال الحفظ
        const originalSaveToStorage = window.saveToStorage;
        if (originalSaveToStorage) {
            window.saveToStorage = function(key, data) {
                originalSaveToStorage(key, data);
                
                // إرسال التحديث للنوافذ الأخرى
                if (syncSystem) {
                    syncSystem.broadcastUpdate(key, data);
                }
            };
        }
    }
});

// إضافة CSS للإشعارات
const syncStyles = `
    .sync-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        min-width: 300px;
        animation: slideInRight 0.3s ease-out;
    }

    .sync-notification.update {
        border-left: 4px solid #17a2b8;
    }

    .sync-notification.activity {
        border-left: 4px solid #28a745;
    }

    .notification-content {
        padding: 15px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .notification-content i {
        color: #17a2b8;
        font-size: 18px;
    }

    .notification-content span {
        flex: 1;
        font-size: 14px;
        color: #333;
    }

    .notification-content button {
        background: none;
        border: none;
        font-size: 18px;
        color: #999;
        cursor: pointer;
        padding: 0;
        width: 20px;
        height: 20px;
    }

    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @media (max-width: 768px) {
        .sync-notification {
            right: 10px;
            left: 10px;
            min-width: auto;
        }
    }
`;

// إضافة الأنماط للصفحة
const styleSheet = document.createElement('style');
styleSheet.textContent = syncStyles;
document.head.appendChild(styleSheet);

// تصدير نظام المزامنة
window.syncSystem = syncSystem;
