<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أزرار نظام الطلاب</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
            margin: 20px;
            background: #f8f9fa;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
        }
        .test-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار أزرار نظام إدارة الطلاب</h1>
        
        <div class="test-section">
            <h3>📋 اختبار الوظائف الأساسية</h3>
            <button class="test-btn" onclick="testBasicFunctions()">اختبار الوظائف الأساسية</button>
            <button class="test-btn" onclick="testStudentsPage()">اختبار صفحة الطلاب</button>
            <div id="basic-test-result"></div>
        </div>

        <div class="test-section">
            <h3>🔗 اختبار الروابط</h3>
            <button class="test-btn" onclick="window.open('students.html', '_blank')">فتح صفحة الطلاب</button>
            <button class="test-btn" onclick="window.open('index.html', '_blank')">فتح لوحة التحكم</button>
            <div id="links-test-result"></div>
        </div>

        <div class="test-section">
            <h3>💾 اختبار التخزين المحلي</h3>
            <button class="test-btn" onclick="testLocalStorage()">اختبار التخزين</button>
            <button class="test-btn" onclick="clearTestData()">مسح بيانات الاختبار</button>
            <div id="storage-test-result"></div>
        </div>

        <div class="test-section">
            <h3>🔧 أدوات الإصلاح</h3>
            <button class="test-btn" onclick="fixStudentsPage()">إصلاح صفحة الطلاب</button>
            <button class="test-btn" onclick="resetSystem()">إعادة تعيين النظام</button>
            <div id="fix-test-result"></div>
        </div>

        <div class="test-section">
            <h3>📊 معلومات النظام</h3>
            <div id="system-info"></div>
        </div>
    </div>

    <script>
        // اختبار الوظائف الأساسية
        function testBasicFunctions() {
            const result = document.getElementById('basic-test-result');
            let html = '<div class="info">جاري الاختبار...</div>';
            result.innerHTML = html;

            setTimeout(() => {
                let tests = [];
                
                // اختبار وجود localStorage
                try {
                    localStorage.setItem('test', 'test');
                    localStorage.removeItem('test');
                    tests.push('✅ التخزين المحلي يعمل');
                } catch (e) {
                    tests.push('❌ مشكلة في التخزين المحلي');
                }

                // اختبار وجود البيانات
                const students = localStorage.getItem('sbea_students');
                if (students) {
                    tests.push('✅ بيانات الطلاب موجودة');
                } else {
                    tests.push('⚠️ لا توجد بيانات طلاب');
                }

                // اختبار المتصفح
                tests.push(`✅ المتصفح: ${navigator.userAgent.split(' ')[0]}`);
                
                result.innerHTML = `<div class="success">${tests.join('<br>')}</div>`;
            }, 1000);
        }

        // اختبار صفحة الطلاب
        function testStudentsPage() {
            const result = document.getElementById('basic-test-result');
            result.innerHTML = '<div class="info">جاري اختبار صفحة الطلاب...</div>';

            // فتح صفحة الطلاب في نافذة جديدة
            const studentsWindow = window.open('students.html', 'studentsTest');
            
            setTimeout(() => {
                try {
                    if (studentsWindow && !studentsWindow.closed) {
                        result.innerHTML = '<div class="success">✅ تم فتح صفحة الطلاب بنجاح</div>';
                    } else {
                        result.innerHTML = '<div class="error">❌ فشل في فتح صفحة الطلاب</div>';
                    }
                } catch (e) {
                    result.innerHTML = '<div class="error">❌ خطأ في الوصول لصفحة الطلاب</div>';
                }
            }, 2000);
        }

        // اختبار التخزين المحلي
        function testLocalStorage() {
            const result = document.getElementById('storage-test-result');
            
            try {
                // إنشاء بيانات اختبار
                const testData = {
                    id: 'test_student_001',
                    name: 'طالب اختبار',
                    level: 'الأولى ابتدائي',
                    group: 'أ',
                    phone: '0123456789',
                    fee: 500,
                    barcode: '000001',
                    createdAt: new Date().toISOString()
                };

                localStorage.setItem('test_student_data', JSON.stringify(testData));
                const retrieved = JSON.parse(localStorage.getItem('test_student_data'));

                if (retrieved && retrieved.name === testData.name) {
                    result.innerHTML = '<div class="success">✅ التخزين المحلي يعمل بشكل صحيح</div>';
                } else {
                    result.innerHTML = '<div class="error">❌ مشكلة في استرجاع البيانات</div>';
                }
            } catch (e) {
                result.innerHTML = `<div class="error">❌ خطأ في التخزين: ${e.message}</div>`;
            }
        }

        // مسح بيانات الاختبار
        function clearTestData() {
            const result = document.getElementById('storage-test-result');
            
            try {
                localStorage.removeItem('test_student_data');
                result.innerHTML = '<div class="success">✅ تم مسح بيانات الاختبار</div>';
            } catch (e) {
                result.innerHTML = `<div class="error">❌ خطأ في المسح: ${e.message}</div>`;
            }
        }

        // إصلاح صفحة الطلاب
        function fixStudentsPage() {
            const result = document.getElementById('fix-test-result');
            result.innerHTML = '<div class="info">جاري الإصلاح...</div>';

            // محاولة إصلاح المشاكل الشائعة
            setTimeout(() => {
                try {
                    // التأكد من وجود الرقم التسلسلي
                    if (!localStorage.getItem('sbea_next_student_id')) {
                        localStorage.setItem('sbea_next_student_id', '1');
                    }

                    // التأكد من وجود مصفوفة الطلاب
                    if (!localStorage.getItem('sbea_students')) {
                        localStorage.setItem('sbea_students', '[]');
                    }

                    result.innerHTML = '<div class="success">✅ تم إصلاح المشاكل الأساسية</div>';
                } catch (e) {
                    result.innerHTML = `<div class="error">❌ فشل الإصلاح: ${e.message}</div>`;
                }
            }, 1000);
        }

        // إعادة تعيين النظام
        function resetSystem() {
            const result = document.getElementById('fix-test-result');
            
            if (confirm('هل أنت متأكد من إعادة تعيين النظام؟ سيتم حذف جميع البيانات!')) {
                try {
                    // مسح جميع البيانات المتعلقة بالنظام
                    const keys = Object.keys(localStorage);
                    keys.forEach(key => {
                        if (key.startsWith('sbea_') || key.startsWith('nour_')) {
                            localStorage.removeItem(key);
                        }
                    });

                    result.innerHTML = '<div class="success">✅ تم إعادة تعيين النظام بنجاح</div>';
                } catch (e) {
                    result.innerHTML = `<div class="error">❌ فشل في إعادة التعيين: ${e.message}</div>`;
                }
            }
        }

        // عرض معلومات النظام
        function showSystemInfo() {
            const info = document.getElementById('system-info');
            
            const students = localStorage.getItem('sbea_students');
            const nextId = localStorage.getItem('sbea_next_student_id');
            const studentsCount = students ? JSON.parse(students).length : 0;

            info.innerHTML = `
                <div class="info">
                    <strong>📊 معلومات النظام:</strong><br>
                    عدد الطلاب: ${studentsCount}<br>
                    الرقم التسلسلي التالي: ${nextId || 'غير محدد'}<br>
                    حجم البيانات: ${JSON.stringify(localStorage).length} حرف<br>
                    الوقت الحالي: ${new Date().toLocaleString('ar-MA')}
                </div>
            `;
        }

        // تشغيل عرض المعلومات عند التحميل
        document.addEventListener('DOMContentLoaded', showSystemInfo);
    </script>
</body>
</html>
