document.addEventListener('DOMContentLoaded', () => {
    // =================================================================
    //                      Constants & Storage
    // =================================================================
    const ACADEMIC_LEVELS = [
        'التحضيري',
        'الأول ابتدائي',
        'الثاني ابتدائي',
        'الثالث ابتدائي',
        'الرابع ابتدائي',
        'الخامس ابتدائي',
        'السادس ابتدائي',
        'الأولى إعدادي',
        'الثانية إعدادي',
        'الثالثة إعدادي',
        'الجذع المشترك',
        'الأولى بكالوريا',
        'الثانية بكالوريا'
    ];

    // أسماء الأفواج المتاحة
    const AVAILABLE_GROUPS = [
        'فوج أ',
        'فوج ب',
        'فوج ج',
        'فوج د',
        'فوج هـ',
        'فوج و'
    ];

    // أشهر السنة الدراسية
    const ACADEMIC_MONTHS = [
        "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر", "يناير",
        "فبراير", "مارس", "أبريل", "مايو", "يونيو"
    ];



    // حالات الدفع - تم استخدامها في نظام الدفعات
    window.PAYMENT_STATUS = {
        PAID: 'مدفوع',
        PARTIAL: 'جزئي',
        UNPAID: 'غير مدفوع'
    };

    function getFromStorage(key) {
        return JSON.parse(localStorage.getItem(key)) || [];
    }

    function saveToStorage(key, data) {
        localStorage.setItem(key, JSON.stringify(data));
    }

    // دالة للحصول على الرقم التسلسلي التالي
    function getNextStudentId() {
        // الحصول على آخر رقم تسلسلي مستخدم من التخزين المحلي
        let lastUsedId = parseInt(localStorage.getItem('sbea_last_student_id') || '0');
        const students = getFromStorage('sbea_students');

        // التأكد من أن الرقم التسلسلي أكبر من أي رقم موجود حالياً
        if (students.length > 0) {
            const maxExistingId = Math.max(...students.map(s => parseInt(s.barcode) || 0));
            lastUsedId = Math.max(lastUsedId, maxExistingId);
        }

        const nextId = lastUsedId + 1;
        // حفظ الرقم التسلسلي الجديد
        localStorage.setItem('sbea_last_student_id', nextId.toString());
        return nextId;
    }

    let students = getFromStorage('sbea_students');
    let groups = getFromStorage('sbea_groups');
    let teachers = getFromStorage('sbea_teachers');
    let activities = getFromStorage('sbea_activities');
    let curriculum = getFromStorage('sbea_curriculum');
    let inspections = getFromStorage('sbea_inspections');
    let adminNotes = getFromStorage('sbea_admin_notes');
    let competitions = getFromStorage('sbea_competitions');
    let adminStaff = getFromStorage('sbea_admin_staff');
    let guards = getFromStorage('sbea_guards');
    let cleaners = getFromStorage('sbea_cleaners');
    // let teacherSalaries = getFromStorage('sbea_teacher_salaries'); // محفوظ للاستخدام المستقبلي

    // قائمة شاملة لجميع المواد الدراسية
    const ALL_SUBJECTS = [
        "التربية الإسلامية",
        "النشاط العلمي",
        "التربية الفنية",
        "الرياضيات",
        "التربية البدنية",
        "اللغة الإنجليزية",
        "اللغة الفرنسية",
        "اللغة العربية",
        "الاجتماعيات",
        "الفيزياء والكيمياء",
        "علوم الحياة والأرض"
    ];

    const SUBJECTS_BY_LEVEL = {
        "الأول ابتدائي": {
            "اللغة العربية": ["التعبير الكتابي", "تمارين كتابية", "القراءة", "الإملاء", "الخط", "الاستماع والتحدث"],
            "اللغة الفرنسية": ["Dictée", "Activités orales", "Poésie", "Lecture", "Écriture/Copie", "Exercices écrits", "Projet de classe"],
            "مواد أخرى": ["التربية الإسلامية", "النشاط العلمي", "التربية الفنية", "الرياضيات", "التربية البدنية"]
        },
        "الابتدائي (2-6)": {
            "اللغة العربية": ["التعبير الكتابي", "تمارين كتابية", "القراءة", "الإملاء", "الخط", "الاستماع والتحدث"],
            "اللغة الفرنسية": ["Dictée", "Activités orales", "Poésie", "Lecture", "Écriture/Copie", "Exercices écrits", "Projet de classe"],
            "مواد أخرى": ["التربية الإسلامية", "النشاط العلمي", "التربية الفنية", "الرياضيات", "التربية البدنية", "اللغة الإنجليزية"]
        },
        "الإعدادي": {
            "مواد": ["الاجتماعيات", "التربية الإسلامية", "التربية البدنية", "الرياضيات", "الفيزياء والكيمياء", "اللغة الإنجليزية", "اللغة العربية", "اللغة الفرنسية", "علوم الحياة والأرض"]
        }
    };

    // =================================================================
    //                      Page Routing
    // =================================================================
    const currentPage = window.location.pathname.split('/').pop();

    // =================================================================
    //                      Expenses Management
    // =================================================================

    // فئات النفقات الافتراضية
    const DEFAULT_EXPENSE_CATEGORIES = [
        { id: 1, name: 'مواد تعليمية', description: 'كتب، أوراق، أقلام، وسائل تعليمية', color: '#3498db' },
        { id: 2, name: 'رواتب الموظفين', description: 'رواتب الأساتذة والموظفين الإداريين', color: '#e74c3c' },
        { id: 3, name: 'فواتير الخدمات', description: 'كهرباء، ماء، إنترنت، هاتف', color: '#f39c12' },
        { id: 4, name: 'صيانة وإصلاحات', description: 'صيانة المباني والمعدات', color: '#9b59b6' },
        { id: 5, name: 'نظافة ومواد تنظيف', description: 'مواد التنظيف وأدوات النظافة', color: '#2ecc71' },
        { id: 6, name: 'مواصلات ووقود', description: 'وقود الحافلات ومصاريف النقل', color: '#34495e' },
        { id: 7, name: 'أنشطة تعليمية', description: 'رحلات، مسابقات، فعاليات', color: '#e67e22' },
        { id: 8, name: 'مصاريف إدارية', description: 'قرطاسية، طباعة، مصاريف إدارية أخرى', color: '#95a5a6' }
    ];

    // دالة تهيئة فئات النفقات
    function initializeExpenseCategories() {
        const existingCategories = getFromStorage('sbea_expense_categories');
        if (!existingCategories || existingCategories.length === 0) {
            saveToStorage('sbea_expense_categories', DEFAULT_EXPENSE_CATEGORIES);
        }
    }

    // دالة الحصول على الرقم التسلسلي التالي للنفقة
    function getNextExpenseId() {
        let lastUsedId = parseInt(localStorage.getItem('sbea_last_expense_id') || '0');
        const expenses = getFromStorage('sbea_expenses');

        if (expenses.length > 0) {
            const maxExistingId = Math.max(...expenses.map(e => parseInt(e.id) || 0));
            lastUsedId = Math.max(lastUsedId, maxExistingId);
        }

        const nextId = lastUsedId + 1;
        localStorage.setItem('sbea_last_expense_id', nextId.toString());
        return nextId;
    }

    if (currentPage === 'index.html' || currentPage === '') {
        initDashboardPage();
    } else if (currentPage === 'students.html') {
        initStudentsPage();
    } else if (currentPage === 'teachers.html') {
        initTeachersPage();
    } else if (currentPage === 'staff.html') {
        initStaffPage();
    } else if (currentPage === 'groups.html') {
        initGroupsPage();
    } else if (currentPage === 'financial.html') {
        initFinancialPage();
    } else if (currentPage === 'expenses.html') {
        initExpensesPage();
    } else if (currentPage === 'logs.html') {
        initLogsPage();
    } else if (currentPage === 'teacher-students.html') {
        initTeacherStudentsPage();
    } else if (currentPage === 'activities.html') {
        initActivitiesPage();
    } else if (currentPage === 'settings.html') {
        initSettingsPage();
    }

    // =================================================================
    //                      Dashboard Page
    // =================================================================
    function initDashboardPage() {
        updateDashboardStats();

        // Initialize search functionality
        initSearchFunctionality();

        // Add test data if storage is empty (for testing purposes)
        if (students.length === 0) {
            // دالة لإنشاء بيانات الواجبات الشهرية
            function createMonthlyPayments(monthlyFee) {
                const payments = {};
                ACADEMIC_MONTHS.forEach(month => {
                    payments[month] = {
                        status: 'غير مدفوع',
                        amount: 0,
                        remaining: monthlyFee,
                        dueAmount: monthlyFee
                    };
                });
                return payments;
            }

            students = [
                {
                    name: "أحمد محمد علي",
                    level: "الثالث ابتدائي",
                    group: "فوج أ",
                    fee: "5000",
                    paid: true, // للتوافق مع النظام القديم
                    phone: "0555123456",
                    barcode: "000001",
                    picture: null,
                    hasTransport: true,
                    transportFee: "2000",
                    monthlyPayments: createMonthlyPayments(5000),
                    additionalFees: []
                },
                {
                    name: "فاطمة الزهراء",
                    level: "الخامس ابتدائي",
                    group: "فوج ب",
                    fee: "6000",
                    paid: false,
                    phone: "0666789012",
                    barcode: "000002",
                    picture: null,
                    hasTransport: false,
                    transportFee: "0",
                    monthlyPayments: createMonthlyPayments(6000),
                    additionalFees: []
                },
                {
                    name: "يوسف عبد الرحمن",
                    level: "الأولى إعدادي",
                    group: "فوج أ",
                    fee: "7000",
                    paid: true,
                    phone: "0777345678",
                    barcode: "000003",
                    picture: null,
                    hasTransport: true,
                    transportFee: "2500",
                    monthlyPayments: createMonthlyPayments(7000),
                    additionalFees: []
                }
            ];

            // تحديث بعض الدفعات للاختبار
            students[0].monthlyPayments["سبتمبر"] = { status: 'مدفوع', amount: 5000, remaining: 0, dueAmount: 5000 };
            students[0].monthlyPayments["أكتوبر"] = { status: 'جزئي', amount: 3000, remaining: 2000, dueAmount: 5000 };

            students[2].monthlyPayments["سبتمبر"] = { status: 'مدفوع', amount: 7000, remaining: 0, dueAmount: 7000 };

            saveToStorage('sbea_students', students);
        }

        if (teachers.length === 0) {
            teachers = [
                {
                    name: "الأستاذ محمد الأمين",
                    salary: "50000",
                    tasks: "تدريس الرياضيات والفيزياء",
                    levels: ["الثالث ابتدائي", "الخامس ابتدائي"],
                    groups: ["فوج أ", "فوج ب"],
                    subjects: ["الرياضيات", "الفيزياء والكيمياء"],
                    monthlySalaries: createMonthlySalaries(50000)
                },
                {
                    name: "الأستاذة خديجة بن علي",
                    salary: "45000",
                    tasks: "تدريس اللغة العربية والتربية الإسلامية",
                    levels: ["الأولى إعدادي", "الثانية إعدادي"],
                    groups: ["فوج أ"],
                    subjects: ["اللغة العربية", "التربية الإسلامية"],
                    monthlySalaries: createMonthlySalaries(45000)
                },
                {
                    name: "الأستاذ عبد الرحمن الصادق",
                    salary: "42000",
                    tasks: "تدريس اللغة الفرنسية والإنجليزية",
                    levels: ["الخامس ابتدائي", "السادس ابتدائي"],
                    groups: ["فوج أ", "فوج ب"],
                    subjects: ["اللغة الفرنسية", "اللغة الإنجليزية"],
                    monthlySalaries: createMonthlySalaries(42000)
                },
                {
                    name: "الأستاذة فاطمة الزهراء",
                    salary: "38000",
                    tasks: "تدريس النشاط العلمي والتربية الفنية",
                    levels: ["الثالث ابتدائي", "الرابع ابتدائي"],
                    groups: ["فوج أ"],
                    subjects: ["النشاط العلمي", "التربية الفنية"],
                    monthlySalaries: createMonthlySalaries(38000)
                }
            ];
            saveToStorage('sbea_teachers', teachers);
        }

        if (groups.length === 0) {
            groups = [
                { level: "الثالث ابتدائي", name: "فوج أ" },
                { level: "الثالث ابتدائي", name: "فوج ب" },
                { level: "الخامس ابتدائي", name: "فوج أ" },
                { level: "الخامس ابتدائي", name: "فوج ب" },
                { level: "الأولى إعدادي", name: "فوج أ" },
                { level: "الأولى إعدادي", name: "فوج ب" },
                { level: "الثانية إعدادي", name: "فوج أ" },
                { level: "الثانية إعدادي", name: "فوج ب" }
            ];
            saveToStorage('sbea_groups', groups);
        }

        // Update dashboard stats with new data
        updateDashboardStats();
    }

    function updateDashboardStats() {
        // الإحصائيات الأساسية
        document.getElementById('total-students').textContent = students.length;
        document.getElementById('total-groups').textContent = groups.length;
        document.getElementById('total-teachers').textContent = teachers.length;

        // حساب الإحصائيات المالية المتقدمة
        let totalDue = 0;
        let totalPaid = 0;
        let totalRemaining = 0;
        let paidCount = 0;
        let partialCount = 0;
        let unpaidCount = 0;
        let transportStudents = 0;
        let transportFees = 0;

        students.forEach(student => {
            // حساب النقل
            if (student.hasTransport) {
                transportStudents++;
                transportFees += parseFloat(student.transportFee || 0);
            }

            // حساب الرسوم الأساسية للتلميذ
            const monthlyFee = parseFloat(student.monthlyFee || student.fee || 0);
            const transportFee = parseFloat(student.transportFee || 0);
            const registrationFee = parseFloat(student.registrationFee || 0);

            // إجمالي الرسوم السنوية
            const yearlyMonthlyFees = monthlyFee * 10; // 10 أشهر
            const totalStudentFees = yearlyMonthlyFees + transportFee + registrationFee;

            totalDue += totalStudentFees;

            // حساب المدفوع الفعلي
            let studentPaid = 0;

            // حساب الدفعات الشهرية
            if (student.monthlyPayments) {
                Object.values(student.monthlyPayments).forEach(payment => {
                    studentPaid += parseFloat(payment.amount) || 0;

                    if (payment.status === 'مدفوع') paidCount++;
                    else if (payment.status === 'جزئي') partialCount++;
                    else unpaidCount++;
                });
            } else {
                // إذا لم تكن هناك بيانات مدفوعات، اعتبر جميع الأشهر غير مدفوعة
                unpaidCount += 10;
            }

            // إضافة مدفوعات النقل والتسجيل
            if (student.transportPayment) {
                studentPaid += parseFloat(student.transportPayment.amount) || 0;
            }
            if (student.registrationPayment) {
                studentPaid += parseFloat(student.registrationPayment.amount) || 0;
            }

            // للتوافق مع النظام القديم
            if (student.paid && !student.monthlyPayments) {
                studentPaid = totalStudentFees;
                paidCount += 10;
                unpaidCount -= 10;
            }

            totalPaid += studentPaid;

            // حساب الباقي الصحيح
            const studentRemaining = Math.max(0, totalStudentFees - studentPaid);
            totalRemaining += studentRemaining;
        });

        // تحديث الإحصائيات الأساسية
        document.getElementById('total-fees').textContent = totalDue.toLocaleString() + ' DHS';
        document.getElementById('paid-fees').textContent = totalPaid.toLocaleString() + ' DHS';
        document.getElementById('remaining-fees').textContent = totalRemaining.toLocaleString() + ' DHS';

        // حساب النسب المئوية
        const totalPayments = paidCount + partialCount + unpaidCount;
        const paidPercentage = totalPayments > 0 ? (paidCount / totalPayments * 100).toFixed(1) : 0;
        const partialPercentage = totalPayments > 0 ? (partialCount / totalPayments * 100).toFixed(1) : 0;
        const unpaidPercentage = totalPayments > 0 ? (unpaidCount / totalPayments * 100).toFixed(1) : 0;

        // تحديث أشرطة التقدم
        document.getElementById('paid-percentage').style.width = paidPercentage + '%';
        document.getElementById('partial-percentage').style.width = partialPercentage + '%';
        document.getElementById('unpaid-percentage').style.width = unpaidPercentage + '%';

        document.getElementById('paid-percent-text').textContent = paidPercentage + '%';
        document.getElementById('partial-percent-text').textContent = partialPercentage + '%';
        document.getElementById('unpaid-percent-text').textContent = unpaidPercentage + '%';

        // إحصائيات النقل
        document.getElementById('transport-students').textContent = transportStudents;
        document.getElementById('transport-fees').textContent = transportFees.toLocaleString() + ' DHS';
        const transportPercentage = students.length > 0 ? (transportStudents / students.length * 100).toFixed(1) : 0;
        document.getElementById('transport-percentage').textContent = transportPercentage + '%';

        // توزيع التلاميذ حسب المستوى
        updateLevelDistribution();
    }

    function updateLevelDistribution() {
        const levelCounts = {};
        students.forEach(student => {
            levelCounts[student.level] = (levelCounts[student.level] || 0) + 1;
        });

        const levelDistributionDiv = document.getElementById('level-distribution');
        let html = '';

        Object.entries(levelCounts).forEach(([level, count]) => {
            const percentage = students.length > 0 ? (count / students.length * 100).toFixed(1) : 0;
            html += `
                <div class="level-item">
                    <span class="level-name">${level}</span>
                    <div class="level-bar">
                        <div class="level-fill" style="width: ${percentage}%"></div>
                    </div>
                    <span class="level-count">${count} (${percentage}%)</span>
                </div>
            `;
        });

        levelDistributionDiv.innerHTML = html;
    }

    function initSearchFunctionality() {
        const searchNameInput = document.getElementById('search-name');
        const searchLevelInput = document.getElementById('search-level');
        const searchBarcodeInput = document.getElementById('search-barcode');
        const searchTypeInput = document.getElementById('search-type');
        const searchBtn = document.getElementById('search-btn');
        const clearSearchBtn = document.getElementById('clear-search-btn');
        const searchResults = document.getElementById('search-results');

        // Populate levels dropdown
        ACADEMIC_LEVELS.forEach(level => {
            const option = document.createElement('option');
            option.value = level;
            option.textContent = level;
            searchLevelInput.appendChild(option);
        });

        function performSearch() {
            const nameQuery = searchNameInput.value.toLowerCase().trim();
            const levelQuery = searchLevelInput.value;
            const barcodeQuery = searchBarcodeInput.value.trim();
            const typeQuery = searchTypeInput.value;

            let results = [];

            // Search students
            if (typeQuery === 'all' || typeQuery === 'students') {
                const studentResults = students.filter(student => {
                    const nameMatch = !nameQuery || student.name.toLowerCase().includes(nameQuery);
                    const levelMatch = !levelQuery || student.level === levelQuery;
                    const barcodeMatch = !barcodeQuery || student.barcode.includes(barcodeQuery);
                    return nameMatch && levelMatch && barcodeMatch;
                }).map(student => ({
                    type: 'student',
                    name: student.name,
                    level: student.level,
                    group: student.group,
                    barcode: student.barcode,
                    phone: student.phone,
                    fee: student.fee,
                    paid: student.paid,
                    picture: student.picture
                }));
                results = results.concat(studentResults);
            }

            // Search teachers
            if (typeQuery === 'all' || typeQuery === 'teachers') {
                const teacherResults = teachers.filter(teacher => {
                    const nameMatch = !nameQuery || teacher.name.toLowerCase().includes(nameQuery);
                    const levelMatch = !levelQuery || teacher.levels.includes(levelQuery);
                    return nameMatch && levelMatch;
                }).map(teacher => ({
                    type: 'teacher',
                    name: teacher.name,
                    levels: teacher.levels,
                    groups: teacher.groups,
                    subjects: teacher.subjects,
                    salary: teacher.salary,
                    tasks: teacher.tasks
                }));
                results = results.concat(teacherResults);
            }

            displayResults(results);
        }

        function displayResults(results) {
            if (results.length === 0) {
                searchResults.innerHTML = '<p class="no-results">لم يتم العثور على نتائج مطابقة للبحث.</p>';
                return;
            }

            let html = '<h3>نتائج البحث:</h3><div class="results-grid">';

            results.forEach(item => {
                if (item.type === 'student') {
                    html += `
                        <div class="result-card student-card">
                            <div class="result-header">
                                <img src="${item.picture || 'logo'}" alt="صورة ${item.name}" class="result-image">
                                <h4>${item.name}</h4>
                                <span class="result-type">تلميذ</span>
                            </div>
                            <div class="result-details">
                                <p><strong>المستوى:</strong> ${item.level}</p>
                                <p><strong>الفوج:</strong> ${item.group}</p>
                                <p><strong>الرمز الشريطي:</strong> ${item.barcode}</p>
                                <p><strong>الهاتف:</strong> ${item.phone}</p>
                                <p><strong>المستحقات:</strong> ${item.fee} DHS</p>
                                <p class="${item.paid ? 'paid' : 'unpaid'}"><strong>الحالة:</strong> ${item.paid ? 'مدفوع' : 'غير مدفوع'}</p>
                            </div>
                        </div>
                    `;
                } else if (item.type === 'teacher') {
                    html += `
                        <div class="result-card teacher-card">
                            <div class="result-header">
                                <img src="logo" alt="صورة ${item.name}" class="result-image">
                                <h4>${item.name}</h4>
                                <span class="result-type">أستاذ</span>
                            </div>
                            <div class="result-details">
                                <p><strong>المستويات:</strong> ${item.levels.join(', ')}</p>
                                <p><strong>الأفواج:</strong> ${item.groups.join(', ')}</p>
                                <p><strong>المواد:</strong> ${item.subjects.join(', ')}</p>
                                <p><strong>الراتب:</strong> ${item.salary || 'غير محدد'}</p>
                                <p><strong>المهام:</strong> ${item.tasks || 'غير محددة'}</p>
                            </div>
                        </div>
                    `;
                }
            });

            html += '</div>';
            searchResults.innerHTML = html;
        }

        function clearSearch() {
            searchNameInput.value = '';
            searchLevelInput.value = '';
            searchBarcodeInput.value = '';
            searchTypeInput.value = 'all';
            searchResults.innerHTML = '';
        }

        // Event listeners
        searchBtn.addEventListener('click', performSearch);
        clearSearchBtn.addEventListener('click', clearSearch);

        // Real-time search on input
        searchNameInput.addEventListener('input', performSearch);
        searchLevelInput.addEventListener('change', performSearch);
        searchBarcodeInput.addEventListener('input', performSearch);
        searchTypeInput.addEventListener('change', performSearch);
    }

    // =================================================================
    //                      Students Page
    // =================================================================
    function initStudentsPage() {
        // التحقق من وجود الصفحة الجديدة
        const studentsContainer = document.querySelector('.students-container');
        const newStudentsTable = document.getElementById('students-table');

        if (studentsContainer || newStudentsTable) {
            console.log('تم تحميل صفحة التلاميذ الجديدة بنجاح');
            // الصفحة الجديدة تحتوي على JavaScript مدمج
            return;
        }

        // النظام القديم للتوافق مع الإصدارات السابقة
        const studentForm = document.getElementById('add-student-form');
        const studentTableBody = document.querySelector('#student-table tbody');
        const searchNameInput = document.getElementById('search-name');
        const searchLevelInput = document.getElementById('search-level');
        const searchBarcodeInput = document.getElementById('search-barcode');
        const printStudentsBtn = document.getElementById('print-students-btn');
        const studentLevelSelect = document.getElementById('student-level');
        const studentGroupSelect = document.getElementById('student-group');
        const importExcelBtn = document.getElementById('import-excel-btn');
        const excelFileInput = document.getElementById('excel-file-input');
        const downloadTemplateBtn = document.getElementById('download-template-btn');

        if (!studentForm || !studentTableBody) {
            console.log('عناصر صفحة التلاميذ غير موجودة');
            return;
        }

        // تحميل البيانات للنظام القديم
        let students = loadFromStorage('sbea_students') || [];

        // دالة الحصول على الرقم التسلسلي التالي
        function getNextStudentId() {
            const savedNextId = localStorage.getItem('sbea_next_student_id');
            if (savedNextId) {
                const nextId = parseInt(savedNextId);
                localStorage.setItem('sbea_next_student_id', (nextId + 1).toString());
                return nextId;
            } else {
                // إذا لم يكن هناك رقم محفوظ، احسب الرقم التالي
                if (students.length === 0) {
                    localStorage.setItem('sbea_next_student_id', '2');
                    return 1;
                } else {
                    // العثور على أعلى رقم تسلسلي موجود
                    const maxId = Math.max(...students.map(s => parseInt(s.barcode) || 0));
                    const nextId = maxId + 1;
                    localStorage.setItem('sbea_next_student_id', (nextId + 1).toString());
                    return nextId;
                }
            }
        }

        function populateLevelsDropdown(selectElement) {
            ACADEMIC_LEVELS.forEach(level => {
                const option = document.createElement('option');
                option.value = level;
                option.textContent = level;
                selectElement.appendChild(option.cloneNode(true));
            });
             // Also populate search dropdown
            ACADEMIC_LEVELS.forEach(level => {
                const option = document.createElement('option');
                option.value = level;
                option.textContent = level;
                searchLevelInput.appendChild(option);
            });
        }

        function updateGroupsDropdown(selectedLevel) {
            studentGroupSelect.innerHTML = '<option value="">اختر الفوج</option>';
            const filteredGroups = groups.filter(g => g.level === selectedLevel);
            filteredGroups.forEach(group => {
                const option = document.createElement('option');
                option.value = group.name;
                option.textContent = group.name;
                studentGroupSelect.appendChild(option);
            });
        }

        studentLevelSelect.addEventListener('change', () => {
            updateGroupsDropdown(studentLevelSelect.value);
        });

        function renderStudents() {
            const nameFilter = searchNameInput.value.toLowerCase();
            const levelFilter = searchLevelInput.value;
            const barcodeFilter = searchBarcodeInput.value;

            const filteredStudents = students.filter(s => {
                const nameMatch = s.name.toLowerCase().includes(nameFilter);
                const levelMatch = !levelFilter || s.level === levelFilter;
                const barcodeMatch = !barcodeFilter || s.barcode.includes(barcodeFilter);
                return nameMatch && levelMatch && barcodeMatch;
            });

            studentTableBody.innerHTML = '';
            filteredStudents.forEach((student, index) => {
                addStudentToTable(student, index + 1);
            });
        }

        function addStudentToTable(student, serialNumber) {
            const { name, level, group, fee, phone, barcode, picture, hasTransport, transportFee, monthlyPayments } = student;
            const paddedSerialNumber = serialNumber.toString().padStart(6, '0');
            const row = document.createElement('tr');
            // Use barcode as the unique identifier
            row.dataset.barcode = barcode;

            // حساب إجمالي المدفوع والباقي الصحيح
            let totalPaid = 0;
            let totalRemaining = 0;

            const monthlyFee = parseFloat(fee) || 0;
            const transportFeeAmount = parseFloat(transportFee) || 0;
            const registrationFeeAmount = parseFloat(student.registrationFee) || 0;

            // إجمالي الرسوم السنوية
            const totalYearlyFees = (monthlyFee * 10) + transportFeeAmount + registrationFeeAmount;

            // حساب المدفوع من الدفعات الشهرية
            if (monthlyPayments) {
                Object.values(monthlyPayments).forEach(payment => {
                    totalPaid += parseFloat(payment.amount) || 0;
                });
            }

            // إضافة مدفوعات النقل والتسجيل
            if (student.transportPayment) {
                totalPaid += parseFloat(student.transportPayment.amount) || 0;
            }
            if (student.registrationPayment) {
                totalPaid += parseFloat(student.registrationPayment.amount) || 0;
            }

            // حساب الباقي الصحيح
            totalRemaining = Math.max(0, totalYearlyFees - totalPaid);

            row.innerHTML = `
                <td><img src="${picture || `${paddedSerialNumber}.jpg`}" alt="صورة شخصية" class="student-picture-thumbnail" onerror="this.src='logo'"></td>
                <td>${paddedSerialNumber}</td>
                <td>${name}</td>
                <td>${level}</td>
                <td>${group}</td>
                <td>${fee} DHS</td>
                <td class="${hasTransport ? 'transport-yes' : 'transport-no'}">${hasTransport ? `نعم (${transportFee} DHS)` : 'لا'}</td>
                <td class="paid">${totalPaid.toLocaleString()} DHS</td>
                <td class="${totalRemaining > 0 ? 'unpaid' : 'paid'}">${totalRemaining.toLocaleString()} DHS</td>
                <td>${phone}</td>
                <td><svg id="barcode-${barcode}"></svg></td>
                <td>
                    <button onclick="openEditModal('${barcode}')" class="edit-btn">تعديل</button>
                    <button onclick="deleteStudent('${barcode}')" class="delete-btn">حذف</button>
                    <button onclick="openPaymentsModal('${barcode}')" class="payments-btn">الدفعات</button>
                    ${phone ? `<button onclick="openWhatsAppChat('${phone}', '${name}')" class="whatsapp-btn" title="محادثة WhatsApp">
                        <i class="fab fa-whatsapp"></i>
                    </button>` : ''}
                </td>
            `;
            studentTableBody.appendChild(row);
            // Use the student's own barcode property for the JsBarcode
            if (barcode && typeof window.JsBarcode !== 'undefined') {
                try {
                    window.JsBarcode(`#barcode-${barcode}`, barcode, {
                        height: 40,
                        displayValue: true,
                        margin: 0,
                        fontSize: 14,
                        format: "CODE128"
                    });
                } catch (error) {
                    console.warn('خطأ في إنشاء الرمز الشريطي:', error);
                    // إضافة نص بديل إذا فشل إنشاء الرمز الشريطي
                    const barcodeElement = document.getElementById(`barcode-${barcode}`);
                    if (barcodeElement) {
                        barcodeElement.innerHTML = `<div style="text-align: center; padding: 10px; border: 1px solid #ccc;">${barcode}</div>`;
                    }
                }
            }

            row.querySelector('.delete-btn').addEventListener('click', (e) => {
                const targetBarcode = e.target.closest('tr').dataset.barcode;
                const studentIndex = students.findIndex(s => s.barcode === targetBarcode);
                if (studentIndex > -1) {
                    students.splice(studentIndex, 1);
                    saveToStorage('sbea_students', students);
                    renderStudents();
                }
            });
            row.querySelector('.payments-btn').addEventListener('click', (e) => {
                const targetBarcode = e.target.closest('tr').dataset.barcode;
                const studentIndex = students.findIndex(s => s.barcode === targetBarcode);
                if (studentIndex > -1) {
                    openPaymentsModal(students[studentIndex]);
                }
            });
            row.querySelector('.whatsapp-btn').addEventListener('click', () => {
                const internationalPhone = phone.startsWith('0') ? `213${phone.substring(1)}` : phone;
                window.open(`https://wa.me/${internationalPhone}`, '_blank');
            });
            row.querySelector('.edit-btn').addEventListener('click', e => {
                const targetBarcode = e.target.closest('tr').dataset.barcode;
                const studentIndex = students.findIndex(s => s.barcode === targetBarcode);
                if (studentIndex > -1) {
                    openEditModal(students[studentIndex]);
                }
            });
        }

        // إضافة معالج لإظهار/إخفاء حقل رسوم النقل
        const transportCheckbox = document.getElementById('student-transport');
        const transportFeeContainer = document.getElementById('transport-fee-container');

        transportCheckbox.addEventListener('change', () => {
            transportFeeContainer.style.display = transportCheckbox.checked ? 'block' : 'none';
        });

        studentForm.addEventListener('submit', async e => {
            e.preventDefault();

            // عرض محمل
            const submitBtn = studentForm.querySelector('button[type="submit"]');
            window.loadingSystem.loadButton(submitBtn, 'جاري الإضافة...');

            try {
                // جمع البيانات من النموذج
                const formData = {
                    name: document.getElementById('student-name').value.trim(),
                    level: document.getElementById('student-level').value,
                    group: document.getElementById('student-group').value,
                    fee: document.getElementById('student-fee').value.trim(),
                    phone: document.getElementById('student-phone').value.trim(),
                    transport: document.getElementById('student-transport').checked,
                    transportFee: document.getElementById('student-transport-fee').value || 0
                };

                // تنظيف البيانات
                const sanitizedData = window.validator.sanitizeData(formData);

                // التحقق من صحة البيانات
                const validation = window.validator.validateForm(sanitizedData, {
                    name: ['required', 'arabicName', { name: 'minLength', params: [2] }],
                    level: ['required'],
                    group: ['required'],
                    fee: ['required', 'positiveNumber'],
                    phone: ['phone']
                });

                if (!validation.isValid) {
                    // عرض أول خطأ
                    const firstError = Object.values(validation.errors)[0][0];
                    window.showError(firstError.message, 'خطأ في البيانات');

                    // تمييز الحقل الخاطئ
                    const fieldElement = document.getElementById(`student-${Object.keys(validation.errors)[0]}`);
                    if (fieldElement) {
                        window.shake(fieldElement);
                        fieldElement.focus();
                    }
                    return;
                }

                // معالجة الصورة
                const pictureInput = document.getElementById('student-picture');
                let pictureDataUrl = null;

                if (pictureInput.files && pictureInput.files[0]) {
                    try {
                        pictureDataUrl = await new Promise((resolve, reject) => {
                            const reader = new FileReader();
                            reader.onload = () => resolve(reader.result);
                            reader.onerror = reject;
                            reader.readAsDataURL(pictureInput.files[0]);
                        });
                    } catch (error) {
                        console.error("Error reading file:", error);
                        window.showError("حدث خطأ أثناء تحميل الصورة", "خطأ في الصورة");
                        return;
                    }
                }

                const monthlyFee = parseFloat(sanitizedData.fee);
                const hasTransport = sanitizedData.transport;
                const transportFee = hasTransport ? parseFloat(sanitizedData.transportFee || 0) : 0;

                // إنشاء بيانات الواجبات الشهرية
                const monthlyPayments = {};
                ACADEMIC_MONTHS.forEach(month => {
                    monthlyPayments[month] = {
                        status: 'غير مدفوع',
                        amount: 0,
                        remaining: monthlyFee,
                        dueAmount: monthlyFee
                    };
                });

                // قراءة المصاريف السنوية
                const registrationFee = parseFloat(document.getElementById('registration-fee').value || 0);
                const booksFee = parseFloat(document.getElementById('books-fee').value || 0);

                const newStudent = {
                    name: sanitizedData.name,
                    level: sanitizedData.level,
                    group: sanitizedData.group,
                    fee: monthlyFee.toString(),
                    paid: false, // للتوافق مع النظام القديم
                    phone: sanitizedData.phone,
                    picture: pictureDataUrl,
                    hasTransport: hasTransport,
                    transportFee: transportFee.toString(),
                    monthlyPayments: monthlyPayments,
                    additionalFees: [],
                    barcode: '', // Will be set after pushing to the array
                    annualFees: {
                        registration: registrationFee,
                        books: booksFee,
                        registrationPaid: false,
                        booksPaid: false
                    },
                    createdAt: new Date().toISOString()
                };

                // الحصول على الرقم التسلسلي التالي
                const nextId = getNextStudentId();
                newStudent.barcode = nextId.toString().padStart(6, '0');

                students.push(newStudent);

                saveToStorage('sbea_students', students);
                renderStudents();
                studentForm.reset();
                studentGroupSelect.innerHTML = '<option value="">اختر الفوج</option>';
                transportFeeContainer.style.display = 'none';

                // عرض رسالة نجاح
                window.showSuccess(`تم إضافة التلميذ "${newStudent.name}" بنجاح`, 'تمت الإضافة');

                // تسجيل النشاط
                if (window.authSystem) {
                    window.authSystem.logUserActivity('create', `إضافة التلميذ: ${newStudent.name}`);
                }

                // إشعار المزامنة
                if (window.syncSystem) {
                    window.syncSystem.broadcastActivity('student_added', `تم إضافة التلميذ: ${newStudent.name}`);
                }

            } catch (error) {
                console.error('خطأ في إضافة التلميذ:', error);
                window.showError('حدث خطأ في إضافة التلميذ', 'خطأ');
            } finally {
                // إيقاف تحميل الزر
                window.loadingSystem.unloadButton(submitBtn);
            }
        });

        importExcelBtn.addEventListener('click', () => excelFileInput.click());
        excelFileInput.addEventListener('change', e => {
            const file = e.target.files[0];
            const reader = new FileReader();
            reader.onload = (event) => {
                const data = new Uint8Array(event.target.result);
                const workbook = XLSX.read(data, { type: 'array' });
                const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
                const jsonData = XLSX.utils.sheet_to_json(firstSheet, { header: 1 });
                
                jsonData.slice(1).forEach(row => {
                    if (row[0] && row[1] && row[2] && row[3] && row[4]) {
                        // الحصول على الرقم التسلسلي التالي
                        const nextId = getNextStudentId();
                        const newStudent = {
                            name: row[0],
                            level: row[1],
                            group: row[2],
                            fee: row[3],
                            paid: false,
                            phone: row[4],
                            picture: null, // No picture from Excel import
                            barcode: nextId.toString().padStart(6, '0'),
                            hasTransport: false,
                            transportFee: '0',
                            monthlyPayments: {
                                'سبتمبر': { paid: false, amount: 0, date: null },
                                'أكتوبر': { paid: false, amount: 0, date: null },
                                'نوفمبر': { paid: false, amount: 0, date: null },
                                'ديسمبر': { paid: false, amount: 0, date: null },
                                'يناير': { paid: false, amount: 0, date: null },
                                'فبراير': { paid: false, amount: 0, date: null },
                                'مارس': { paid: false, amount: 0, date: null },
                                'أبريل': { paid: false, amount: 0, date: null },
                                'مايو': { paid: false, amount: 0, date: null },
                                'يونيو': { paid: false, amount: 0, date: null }
                            },
                            additionalFees: [],
                            annualFees: {
                                registration: 0,
                                books: 0,
                                registrationPaid: false,
                                booksPaid: false
                            },
                            createdAt: new Date().toISOString()
                        };
                        students.push(newStudent);
                        const groupExists = groups.some(g => g.level === row[1] && g.name === row[2]);
                        if (!groupExists) {
                            groups.push({ level: row[1], name: row[2] });
                            saveToStorage('sbea_groups', groups);
                        }
                    }
                });
                saveToStorage('sbea_students', students);
                renderStudents();
                alert(`${jsonData.length - 1} تلميذ تم استيرادهم بنجاح!`);
            };
            reader.readAsArrayBuffer(file);
            excelFileInput.value = '';
        });

        downloadTemplateBtn.addEventListener('click', () => {
            const headers = [
                ["الاسم الكامل", "المستوى", "الفوج", "المستحقات الشهرية", "هاتف ولي الأمر"]
            ];
            const ws = XLSX.utils.aoa_to_sheet(headers);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, "نموذج");
            XLSX.writeFile(wb, "نموذج_تلاميذ.xlsx");
        });

        searchNameInput.addEventListener('input', renderStudents);
        searchLevelInput.addEventListener('change', renderStudents);
        searchBarcodeInput.addEventListener('input', renderStudents);

        printStudentsBtn.addEventListener('click', () => window.print());

        populateLevelsDropdown(studentLevelSelect);
        renderStudents();

        // إضافة دوال النوافذ المنبثقة
        initModals();
    }

    // =================================================================
    //                      Modal Functions
    // =================================================================
    function initModals() {
        // إغلاق النوافذ المنبثقة
        document.querySelectorAll('.close-modal, .cancel-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.modal').forEach(modal => {
                    modal.style.display = 'none';
                });
            });
        });

        // إغلاق النافذة عند النقر خارجها
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.style.display = 'none';
            }
        });
    }

    // إنشاء النافذة المنبثقة للمدفوعات إذا لم تكن موجودة
    function createPaymentsModal() {
        if (document.getElementById('payments-modal')) return;

        const modalHTML = `
            <div id="payments-modal" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>إدارة المدفوعات</h3>
                        <span class="close-modal">&times;</span>
                    </div>
                    <div class="modal-body">
                        <div id="student-info"></div>
                        <div id="payments-grid"></div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // إعداد إغلاق النافذة
        const modal = document.getElementById('payments-modal');
        const closeBtn = modal.querySelector('.close-modal');

        closeBtn.addEventListener('click', () => {
            modal.style.display = 'none';
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });
    }

    function openPaymentsModal(student) {
        // التأكد من وجود النافذة المنبثقة
        createPaymentsModal();

        const modal = document.getElementById('payments-modal');
        const studentInfo = document.getElementById('student-info');
        const paymentsGrid = document.getElementById('payments-grid');

        // حساب إجمالي المدفوع والباقي
        let totalPaid = 0;
        let totalRemaining = 0;
        let paidMonths = 0;
        let partialMonths = 0;
        let unpaidMonths = 0;

        if (student.monthlyPayments) {
            Object.values(student.monthlyPayments).forEach(payment => {
                totalPaid += payment.amount || 0;
                totalRemaining += payment.remaining || 0;
                if (payment.status === 'مدفوع') paidMonths++;
                else if (payment.status === 'جزئي') partialMonths++;
                else unpaidMonths++;
            });
        }

        // عرض معلومات التلميذ المحسنة
        studentInfo.innerHTML = `
            <div class="student-summary enhanced">
                <div class="student-header">
                    <img src="${student.picture || `${student.barcode.padStart(6, '0')}.jpg`}" alt="صورة ${student.name}" class="student-modal-image" onerror="this.src='logo'">
                    <div class="student-info">
                        <h4>${student.name}</h4>
                        <p><strong>المستوى:</strong> ${student.level} | <strong>الفوج:</strong> ${student.group}</p>
                        <p><strong>الرقم التسلسلي:</strong> ${student.barcode.padStart(6, '0')}</p>
                    </div>
                </div>
                <div class="payment-summary">
                    <div class="summary-row">
                        <div class="summary-item">
                            <span class="label">المستحقات الشهرية:</span>
                            <span class="value">${student.fee} DHS</span>
                        </div>
                        ${student.hasTransport ? `
                        <div class="summary-item">
                            <span class="label">رسوم النقل الشهرية:</span>
                            <span class="value">${student.transportFee} DHS</span>
                        </div>` : ''}
                    </div>
                    <div class="summary-row">
                        <div class="summary-item success">
                            <span class="label">إجمالي المدفوع:</span>
                            <span class="value">${totalPaid.toLocaleString()} DHS</span>
                        </div>
                        <div class="summary-item ${totalRemaining > 0 ? 'danger' : 'success'}">
                            <span class="label">إجمالي الباقي:</span>
                            <span class="value">${totalRemaining.toLocaleString()} DHS</span>
                        </div>
                    </div>
                    <div class="months-summary">
                        <div class="month-stat paid">
                            <span class="count">${paidMonths}</span>
                            <span class="label">أشهر مدفوعة</span>
                        </div>
                        <div class="month-stat partial">
                            <span class="count">${partialMonths}</span>
                            <span class="label">أشهر جزئية</span>
                        </div>
                        <div class="month-stat unpaid">
                            <span class="count">${unpaidMonths}</span>
                            <span class="label">أشهر غير مدفوعة</span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إعداد التبويبات
        setupPaymentTabs(student);

        // عرض شبكة الدفعات الشهرية
        displayMonthlyPayments(student, paymentsGrid);

        // عرض الواجبات الإضافية
        displayAdditionalFees(student);

        // عرض المصاريف السنوية
        displayAnnualFees(student);

        modal.style.display = 'block';
    }

    function setupPaymentTabs(student) {
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');

        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                // إزالة الفئة النشطة من جميع التبويبات
                tabBtns.forEach(b => b.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));

                // إضافة الفئة النشطة للتبويب المحدد
                btn.classList.add('active');
                const tabId = btn.dataset.tab;
                document.getElementById(`${tabId}-payments`).classList.add('active');
            });
        });

        // إعداد نموذج إضافة الواجبات الإضافية
        const addFeeForm = document.getElementById('add-additional-fee-form');
        addFeeForm.onsubmit = (e) => {
            e.preventDefault();
            addAdditionalFee(student);
        };
    }

    function displayMonthlyPayments(student, paymentsGrid) {
        let paymentsHTML = `
            <div class="payments-header">
                <h4>📅 الدفعات الشهرية للموسم الدراسي (10 أشهر)</h4>
                <p>من شهر سبتمبر إلى شهر يونيو - المستحقات الشهرية: ${student.fee} DHS</p>
            </div>
            <div class="payments-table">
                <table>
                    <thead>
                        <tr>
                            <th>📅 الشهر</th>
                            <th>💰 المبلغ المستحق</th>
                            <th>✅ المبلغ المدفوع</th>
                            <th>⚠️ الباقي</th>
                            <th>📊 الحالة</th>
                            <th>🔧 الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        ACADEMIC_MONTHS.forEach((month, index) => {
            const payment = student.monthlyPayments[month] || {
                status: 'غير مدفوع',
                amount: 0,
                remaining: parseFloat(student.fee),
                dueAmount: parseFloat(student.fee)
            };

            const monthNumber = index + 1;

            paymentsHTML += `
                <tr class="payment-row-${payment.status.replace(' ', '-')}">
                    <td class="month-cell">
                        <div class="month-info">
                            <span class="month-name">${month}</span>
                            <span class="month-number">الشهر ${monthNumber}</span>
                        </div>
                    </td>
                    <td class="amount-cell">${payment.dueAmount.toLocaleString()} DHS</td>
                    <td class="paid-cell">${payment.amount.toLocaleString()} DHS</td>
                    <td class="remaining-cell ${payment.remaining > 0 ? 'has-remaining' : 'no-remaining'}">
                        ${payment.remaining.toLocaleString()} DHS
                    </td>
                    <td class="status-cell">
                        <span class="status-badge status-${payment.status.replace(' ', '-')}">${payment.status}</span>
                    </td>
                    <td class="actions-cell">
                        ${payment.status !== 'مدفوع' ? `
                            <button class="pay-btn" data-month="${month}" data-barcode="${student.barcode}" title="دفع كامل">
                                💰 دفع كامل
                            </button>
                            <button class="partial-pay-btn" data-month="${month}" data-barcode="${student.barcode}" title="دفع جزئي">
                                📝 دفع جزئي
                            </button>
                        ` : `
                            <span class="paid-indicator">✅ مدفوع</span>
                        `}
                    </td>
                </tr>
            `;
        });

        paymentsHTML += '</tbody></table></div>';
        paymentsGrid.innerHTML = paymentsHTML;

        // إضافة معالجات الأحداث لأزرار الدفع
        paymentsGrid.querySelectorAll('.pay-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const month = e.target.dataset.month;
                const barcode = e.target.dataset.barcode;
                processPayment(barcode, month, 'full');
            });
        });

        paymentsGrid.querySelectorAll('.partial-pay-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const month = e.target.dataset.month;
                const barcode = e.target.dataset.barcode;
                const amount = prompt('أدخل المبلغ المدفوع:');
                if (amount && !isNaN(amount) && parseFloat(amount) > 0) {
                    processPayment(barcode, month, 'partial', parseFloat(amount));
                }
            });
        });
    }

    function displayAdditionalFees(student) {
        const additionalFeesList = document.getElementById('additional-fees-list');

        if (!student.additionalFees || student.additionalFees.length === 0) {
            additionalFeesList.innerHTML = '<p class="no-additional-fees">لا توجد واجبات إضافية</p>';
            return;
        }

        let html = '<div class="additional-fees-table"><table><thead><tr><th>الوصف</th><th>المبلغ</th><th>تاريخ الاستحقاق</th><th>الحالة</th><th>الإجراءات</th></tr></thead><tbody>';

        student.additionalFees.forEach((fee, index) => {
            const isOverdue = new Date(fee.dueDate) < new Date() && fee.status !== 'مدفوع';
            html += `
                <tr class="${isOverdue ? 'overdue' : ''}">
                    <td>${fee.description}</td>
                    <td>${fee.amount.toLocaleString()} DHS</td>
                    <td>${new Date(fee.dueDate).toLocaleDateString('ar-DZ')}</td>
                    <td class="status-${fee.status.replace(' ', '-')}">${fee.status}</td>
                    <td>
                        ${fee.status !== 'مدفوع' ? `
                            <button class="pay-additional-btn" data-index="${index}" data-barcode="${student.barcode}">دفع</button>
                            <button class="delete-additional-btn" data-index="${index}" data-barcode="${student.barcode}">حذف</button>
                        ` : ''}
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        additionalFeesList.innerHTML = html;

        // إضافة معالجات الأحداث
        additionalFeesList.querySelectorAll('.pay-additional-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const index = parseInt(e.target.dataset.index);
                const barcode = e.target.dataset.barcode;
                payAdditionalFee(barcode, index);
            });
        });

        additionalFeesList.querySelectorAll('.delete-additional-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const index = parseInt(e.target.dataset.index);
                const barcode = e.target.dataset.barcode;
                deleteAdditionalFee(barcode, index);
            });
        });
    }

    function addAdditionalFee(student) {
        const description = document.getElementById('fee-description').value.trim();
        const amount = parseFloat(document.getElementById('fee-amount').value);
        const dueDate = document.getElementById('fee-due-date').value;

        if (!description || !amount || !dueDate) {
            alert('يرجى ملء جميع الحقول');
            return;
        }

        const newFee = {
            description: description,
            amount: amount,
            dueDate: dueDate,
            status: 'غير مدفوع',
            createdDate: new Date().toISOString()
        };

        if (!student.additionalFees) {
            student.additionalFees = [];
        }

        student.additionalFees.push(newFee);

        // تحديث البيانات في التخزين
        const studentIndex = students.findIndex(s => s.barcode === student.barcode);
        if (studentIndex !== -1) {
            students[studentIndex] = student;
            saveToStorage('sbea_students', students);
        }

        // إعادة عرض الواجبات الإضافية
        displayAdditionalFees(student);

        // مسح النموذج
        document.getElementById('add-additional-fee-form').reset();

        alert('تم إضافة الواجب الإضافي بنجاح');
    }

    function payAdditionalFee(barcode, feeIndex) {
        const studentIndex = students.findIndex(s => s.barcode === barcode);
        if (studentIndex === -1) return;

        students[studentIndex].additionalFees[feeIndex].status = 'مدفوع';
        students[studentIndex].additionalFees[feeIndex].paidDate = new Date().toISOString();

        saveToStorage('sbea_students', students);
        displayAdditionalFees(students[studentIndex]);
        updateDashboardStats();

        alert('تم دفع الواجب الإضافي بنجاح');
    }

    function deleteAdditionalFee(barcode, feeIndex) {
        if (!confirm('هل أنت متأكد من حذف هذا الواجب الإضافي؟')) return;

        const studentIndex = students.findIndex(s => s.barcode === barcode);
        if (studentIndex === -1) return;

        students[studentIndex].additionalFees.splice(feeIndex, 1);

        saveToStorage('sbea_students', students);
        displayAdditionalFees(students[studentIndex]);

        alert('تم حذف الواجب الإضافي بنجاح');
    }

    function processPayment(barcode, month, type, amount = null) {
        const studentIndex = students.findIndex(s => s.barcode === barcode);
        if (studentIndex === -1) return;

        const student = students[studentIndex];
        const payment = student.monthlyPayments[month];
        const dueAmount = payment.dueAmount;

        if (type === 'full') {
            payment.amount = dueAmount;
            payment.remaining = 0;
            payment.status = 'مدفوع';
        } else if (type === 'partial' && amount) {
            const newAmount = Math.min(payment.amount + amount, dueAmount);
            payment.amount = newAmount;
            payment.remaining = dueAmount - newAmount;
            payment.status = payment.remaining > 0 ? 'جزئي' : 'مدفوع';
        }

        saveToStorage('sbea_students', students);
        openPaymentsModal(student); // إعادة فتح النافذة لتحديث البيانات
        renderStudents(); // تحديث الجدول الرئيسي
    }

    function openEditModal(student) {
        const modal = document.getElementById('edit-modal');
        const form = document.getElementById('edit-student-form');

        // ملء النموذج بالبيانات الحالية
        document.getElementById('edit-student-name').value = student.name;
        document.getElementById('edit-student-fee').value = student.fee;
        document.getElementById('edit-student-phone').value = student.phone;
        document.getElementById('edit-student-transport').checked = student.hasTransport;
        document.getElementById('edit-student-transport-fee').value = student.transportFee || '';

        // إظهار/إخفاء حقل رسوم النقل
        const editTransportFeeContainer = document.getElementById('edit-transport-fee-container');
        editTransportFeeContainer.style.display = student.hasTransport ? 'block' : 'none';

        // تحديث المصاريف السنوية
        const editRegistrationFeeInput = document.getElementById('edit-registration-fee');
        const editBooksFeeInput = document.getElementById('edit-books-fee');

        if (editRegistrationFeeInput && student.annualFees) {
            editRegistrationFeeInput.value = student.annualFees.registration || '';
        }

        if (editBooksFeeInput && student.annualFees) {
            editBooksFeeInput.value = student.annualFees.books || '';
        }

        // ملء قوائم المستويات والأفواج
        const editLevelSelect = document.getElementById('edit-student-level');
        const editGroupSelect = document.getElementById('edit-student-group');

        editLevelSelect.innerHTML = '<option value="">اختر المستوى</option>';
        ACADEMIC_LEVELS.forEach(level => {
            const option = document.createElement('option');
            option.value = level;
            option.textContent = level;
            if (level === student.level) option.selected = true;
            editLevelSelect.appendChild(option);
        });

        // تحديث الأفواج عند تغيير المستوى
        function updateEditGroupsDropdown(selectedLevel) {
            editGroupSelect.innerHTML = '<option value="">اختر الفوج</option>';
            const filteredGroups = groups.filter(g => g.level === selectedLevel);
            filteredGroups.forEach(group => {
                const option = document.createElement('option');
                option.value = group.name;
                option.textContent = group.name;
                if (group.name === student.group) option.selected = true;
                editGroupSelect.appendChild(option);
            });
        }

        updateEditGroupsDropdown(student.level);

        editLevelSelect.addEventListener('change', () => {
            updateEditGroupsDropdown(editLevelSelect.value);
        });

        // معالج تغيير النقل
        document.getElementById('edit-student-transport').addEventListener('change', (e) => {
            editTransportFeeContainer.style.display = e.target.checked ? 'block' : 'none';
        });

        // عرض الصورة الحالية
        const currentPicturePreview = document.getElementById('current-picture-preview');
        if (student.picture) {
            currentPicturePreview.innerHTML = `<img src="${student.picture}" alt="الصورة الحالية" style="max-width: 100px; max-height: 100px;">`;
        } else {
            currentPicturePreview.innerHTML = '<p>لا توجد صورة حالية</p>';
        }

        // إزالة معالجات الأحداث السابقة
        form.onsubmit = null;

        // إضافة معالج جديد للنموذج
        const handleFormSubmit = async (e) => {
            e.preventDefault();
            console.log('تم الضغط على زر حفظ التعديلات');
            await updateStudent(student.barcode);
        };

        form.addEventListener('submit', handleFormSubmit);

        // إضافة معالج للزر مباشرة أيضاً
        const submitButton = form.querySelector('button[type="submit"]');
        if (submitButton) {
            submitButton.onclick = async (e) => {
                e.preventDefault();
                console.log('تم الضغط على الزر مباشرة');
                await updateStudent(student.barcode);
            };
        }

        // إضافة معالج لزر الإلغاء
        const cancelButton = form.querySelector('.cancel-btn');
        if (cancelButton) {
            cancelButton.onclick = () => {
                console.log('تم الضغط على زر الإلغاء');
                modal.style.display = 'none';
            };
        }

        modal.style.display = 'block';

        // التأكد من أن النموذج مرئي ومعالجات الأحداث مضافة
        console.log('تم فتح نافذة التحديث للتلميذ:', student.name);
        console.log('معرف النموذج:', form.id);
        console.log('زر الإرسال موجود:', !!submitButton);
    }

    async function updateStudent(barcode) {
        console.log('بدء تحديث التلميذ:', barcode);

        const studentIndex = students.findIndex(s => s.barcode === barcode);
        if (studentIndex === -1) {
            console.error('لم يتم العثور على التلميذ');
            alert('لم يتم العثور على التلميذ!');
            return;
        }

        console.log('تم العثور على التلميذ في الفهرس:', studentIndex);

        const pictureInput = document.getElementById('edit-student-picture');
        let pictureDataUrl = students[studentIndex].picture; // الاحتفاظ بالصورة الحالية

        if (pictureInput.files && pictureInput.files[0]) {
            try {
                pictureDataUrl = await new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = () => resolve(reader.result);
                    reader.onerror = reject;
                    reader.readAsDataURL(pictureInput.files[0]);
                });
            } catch (error) {
                console.error("Error reading file:", error);
                alert("حدث خطأ أثناء تحميل الصورة.");
                return;
            }
        }

        const oldFee = parseFloat(students[studentIndex].fee);
        const newFee = parseFloat(document.getElementById('edit-student-fee').value);

        // تحديث بيانات التلميذ
        students[studentIndex].name = document.getElementById('edit-student-name').value.trim();
        students[studentIndex].level = document.getElementById('edit-student-level').value;
        students[studentIndex].group = document.getElementById('edit-student-group').value;
        students[studentIndex].fee = newFee.toString();
        students[studentIndex].phone = document.getElementById('edit-student-phone').value.trim();
        students[studentIndex].hasTransport = document.getElementById('edit-student-transport').checked;
        students[studentIndex].transportFee = students[studentIndex].hasTransport ?
            document.getElementById('edit-student-transport-fee').value : '0';
        students[studentIndex].picture = pictureDataUrl;

        // تحديث المصاريف السنوية
        const registrationFee = parseFloat(document.getElementById('edit-registration-fee').value || 0);
        const booksFee = parseFloat(document.getElementById('edit-books-fee').value || 0);

        if (!students[studentIndex].annualFees) {
            students[studentIndex].annualFees = {
                registration: 0,
                books: 0,
                registrationPaid: false,
                booksPaid: false
            };
        }

        students[studentIndex].annualFees.registration = registrationFee;
        students[studentIndex].annualFees.books = booksFee;

        // تحديث المستحقات الشهرية إذا تغيرت الرسوم
        if (oldFee !== newFee) {
            Object.keys(students[studentIndex].monthlyPayments).forEach(month => {
                const payment = students[studentIndex].monthlyPayments[month];
                if (payment.status === 'غير مدفوع') {
                    payment.dueAmount = newFee;
                    payment.remaining = newFee;
                }
            });
        }

        console.log('حفظ البيانات...');
        saveToStorage('sbea_students', students);

        console.log('تحديث الجدول...');
        renderStudents();

        console.log('إغلاق النافذة...');
        document.getElementById('edit-modal').style.display = 'none';

        console.log('تم التحديث بنجاح!');
        alert('تم تحديث بيانات التلميذ بنجاح!');
    }

    // =================================================================
    //                      Teachers Page
    // =================================================================
    function initTeachersPage() {
        const teacherForm = document.getElementById('add-teacher-form');
        const teacherTableBody = document.querySelector('#teacher-table tbody');
        const teacherNameInput = document.getElementById('teacher-name');
        const teacherSalaryInput = document.getElementById('teacher-salary');
        const teacherTasksInput = document.getElementById('teacher-tasks');
        const levelsChecklist = document.getElementById('teacher-levels-checklist');
        const groupsChecklist = document.getElementById('teacher-groups-checklist');
        const subjectsChecklist = document.getElementById('teacher-subjects-checklist');

        function populateChecklist(container, items, name) {
            // الاحتفاظ بالعنوان الموجود وإضافة الخيارات فقط
            const existingTitle = container.querySelector('h4');
            const titleText = existingTitle ? existingTitle.textContent : name;

            container.innerHTML = `<h4>${titleText}</h4>`;

            if (items.length === 0) {
                container.innerHTML += '<p class="no-items">لا توجد عناصر متاحة. يرجى إضافتها أولاً.</p>';
                return;
            }

            const checklistContainer = document.createElement('div');
            checklistContainer.className = 'checkbox-list';

            items.forEach(item => {
                const checkboxId = `${name}-${item.replace(/\s+/g, '-').replace(/[^\w\-]/g, '')}`;
                const div = document.createElement('div');
                div.className = 'checkbox-item';
                div.innerHTML = `
                    <input type="checkbox" id="${checkboxId}" name="${name}" value="${item}">
                    <label for="${checkboxId}">${item}</label>
                `;
                checklistContainer.appendChild(div);
            });

            container.appendChild(checklistContainer);
        }

        // دالة الحصول على المواد الفريدة - تم تحديثها للاستخدام
        window.getUniqueSubjects = function() {
            // استخدام القائمة الشاملة للمواد
            return ALL_SUBJECTS;
        };

        // دالة الحصول على الأفواج الفريدة - تم تحديثها للاستخدام
        window.getUniqueGroups = function() {
            // إزالة التكرار من أسماء الأفواج
            const uniqueGroups = [...new Set(groups.map(g => g.name))];
            return uniqueGroups;
        };

        function renderTeachers() {
            teacherTableBody.innerHTML = '';
            teachers.forEach((teacher, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${index + 1}</td>
                    <td>${teacher.name}</td>
                    <td>${teacher.salary || 'N/A'}</td>
                    <td>${teacher.levels.join(', ')}</td>
                    <td>${teacher.groups.join(', ')}</td>
                    <td>${teacher.subjects.join(', ')}</td>
                    <td>${teacher.tasks || 'N/A'}</td>
                    <td><button class="delete-btn">حذف</button></td>
                `;
                row.querySelector('.delete-btn').addEventListener('click', () => {
                    teachers.splice(index, 1);
                    saveToStorage('sbea_teachers', teachers);
                    renderTeachers();
                });
                teacherTableBody.appendChild(row);
            });
        }

        teacherForm.addEventListener('submit', e => {
            e.preventDefault();
            
            const getSelected = (container) => {
                return Array.from(container.querySelectorAll('input[type="checkbox"]:checked')).map(cb => cb.value);
            };

            const monthlySalary = parseFloat(teacherSalaryInput.value.trim());
            const newTeacher = {
                name: teacherNameInput.value.trim(),
                salary: monthlySalary.toString(),
                tasks: teacherTasksInput.value.trim(),
                levels: getSelected(levelsChecklist),
                groups: getSelected(groupsChecklist),
                subjects: getSelected(subjectsChecklist),
                monthlySalaries: createMonthlySalaries(monthlySalary)
            };

            teachers.push(newTeacher);
            saveToStorage('sbea_teachers', teachers);
            renderTeachers();
            teacherForm.reset();
            // Also uncheck all checkboxes
            Array.from(teacherForm.querySelectorAll('input[type="checkbox"]')).forEach(cb => cb.checked = false);
        });

        // التأكد من وجود بيانات الأفواج
        if (groups.length === 0) {
            groups = [
                { level: "الثالث ابتدائي", name: "فوج أ" },
                { level: "الثالث ابتدائي", name: "فوج ب" },
                { level: "الخامس ابتدائي", name: "فوج أ" },
                { level: "الخامس ابتدائي", name: "فوج ب" },
                { level: "الأولى إعدادي", name: "فوج أ" },
                { level: "الأولى إعدادي", name: "فوج ب" }
            ];
            saveToStorage('sbea_groups', groups);
        }

        // تحديث الخيارات في النموذج
        populateTeacherOptions();
        renderTeachers();
    }

    function populateTeacherOptions() {
        const levelsChecklist = document.getElementById('teacher-levels-checklist');
        const groupsChecklist = document.getElementById('teacher-groups-checklist');
        const subjectsChecklist = document.getElementById('teacher-subjects-checklist');

        // دالة مساعدة للحصول على الأفواج الفريدة
        function getUniqueGroupsLocal() {
            const uniqueGroups = [...new Set(groups.map(g => g.name))];
            return uniqueGroups;
        }

        // دالة مساعدة للحصول على المواد
        function getUniqueSubjectsLocal() {
            return ALL_SUBJECTS;
        }

        if (levelsChecklist) {
            levelsChecklist.innerHTML = '';
            const checkboxList = document.createElement('div');
            checkboxList.className = 'checkbox-list';

            ACADEMIC_LEVELS.forEach(level => {
                const checkboxId = `level-${level.replace(/\s+/g, '-').replace(/[^\w\-]/g, '')}`;
                const div = document.createElement('div');
                div.className = 'checkbox-item';
                div.innerHTML = `
                    <input type="checkbox" id="${checkboxId}" name="levels" value="${level}">
                    <label for="${checkboxId}">${level}</label>
                `;
                checkboxList.appendChild(div);
            });

            levelsChecklist.appendChild(checkboxList);
        }

        if (groupsChecklist) {
            groupsChecklist.innerHTML = '';
            const checkboxList = document.createElement('div');
            checkboxList.className = 'checkbox-list';

            const uniqueGroups = getUniqueGroupsLocal();
            uniqueGroups.forEach(group => {
                const checkboxId = `group-${group.replace(/\s+/g, '-').replace(/[^\w\-]/g, '')}`;
                const div = document.createElement('div');
                div.className = 'checkbox-item';
                div.innerHTML = `
                    <input type="checkbox" id="${checkboxId}" name="groups" value="${group}">
                    <label for="${checkboxId}">${group}</label>
                `;
                checkboxList.appendChild(div);
            });

            groupsChecklist.appendChild(checkboxList);
        }

        if (subjectsChecklist) {
            subjectsChecklist.innerHTML = '';
            const checkboxList = document.createElement('div');
            checkboxList.className = 'checkbox-list';

            const subjects = getUniqueSubjectsLocal();
            subjects.forEach(subject => {
                const checkboxId = `subject-${subject.replace(/\s+/g, '-').replace(/[^\w\-]/g, '')}`;
                const div = document.createElement('div');
                div.className = 'checkbox-item';
                div.innerHTML = `
                    <input type="checkbox" id="${checkboxId}" name="subjects" value="${subject}">
                    <label for="${checkboxId}">${subject}</label>
                `;
                checkboxList.appendChild(div);
            });

            subjectsChecklist.appendChild(checkboxList);
        }
    }

    // =================================================================
    //                      Groups Page (Levels & Groups)
    // =================================================================
    function initGroupsPage() {
        const groupForm = document.getElementById('add-group-form');
        const levelSelect = document.getElementById('level-select');
        const groupNameInput = document.getElementById('group-name');
        const listContainer = document.getElementById('levels-and-groups-list');

        function populateLevelsDropdown() {
            ACADEMIC_LEVELS.forEach(level => {
                levelSelect.appendChild(new Option(level, level));
            });
        }

        function getSubjectsForLevel(level) {
            if (level === "الأول ابتدائي") return SUBJECTS_BY_LEVEL["الأول ابتدائي"];
            if (level.includes("ابتدائي") && level !== "الأول ابتدائي") return SUBJECTS_BY_LEVEL["الابتدائي (2-6)"];
            if (level.includes("إعدادي")) return SUBJECTS_BY_LEVEL["الإعدادي"];
            return null;
        }

        function renderLevelsAndGroups() {
            listContainer.innerHTML = '';
            ACADEMIC_LEVELS.forEach(level => {
                const levelContainer = document.createElement('div');
                levelContainer.className = 'level-container';
                
                let subjectsHTML = '<h4>المواد الدراسية:</h4>';
                const subjects = getSubjectsForLevel(level);
                if (subjects) {
                    subjectsHTML += '<div class="subjects-grid">';
                    for (const category in subjects) {
                        subjectsHTML += `<div class="subject-category"><h5>${category}</h5><ul>`;
                        subjects[category].forEach(subject => {
                            subjectsHTML += `<li>${subject}</li>`;
                        });
                        subjectsHTML += '</ul></div>';
                    }
                    subjectsHTML += '</div>';
                } else {
                    subjectsHTML += '<p>لا توجد مواد محددة لهذا المستوى.</p>';
                }

                levelContainer.innerHTML = `<h3>${level}</h3>${subjectsHTML}`;

                const ul = document.createElement('ul');
                ul.innerHTML = '<h4>الأفواج:</h4>';
                const levelGroups = groups.filter(g => g.level === level);
                if (levelGroups.length > 0) {
                    levelGroups.forEach(group => {
                        const li = document.createElement('li');
                        li.textContent = `الفوج: ${group.name}`;
                        const deleteBtn = document.createElement('button');
                        deleteBtn.textContent = 'حذف';
                        deleteBtn.className = 'delete-btn';
                        deleteBtn.onclick = () => {
                            const groupIndex = groups.findIndex(g => g.level === level && g.name === group.name);
                            if (groupIndex > -1) {
                                groups.splice(groupIndex, 1);
                                saveToStorage('sbea_groups', groups);
                                renderLevelsAndGroups();
                            }
                        };
                        li.appendChild(deleteBtn);
                        ul.appendChild(li);
                    });
                } else {
                    const li = document.createElement('li');
                    li.textContent = 'لا توجد أفواج في هذا المستوى.';
                    ul.appendChild(li);
                }
                levelContainer.appendChild(ul);
                listContainer.appendChild(levelContainer);
            });
        }

        groupForm.addEventListener('submit', e => {
            e.preventDefault();
            const level = levelSelect.value;
            const name = groupNameInput.value.trim();
            if (level && name && !groups.some(g => g.level === level && g.name === name)) {
                groups.push({ level, name });
                saveToStorage('sbea_groups', groups);
                renderLevelsAndGroups();
                groupNameInput.value = '';
            } else {
                alert('هذا الفوج موجود بالفعل في هذا المستوى.');
            }
        });

        populateLevelsDropdown();
        renderLevelsAndGroups();
        initAdvancedManagement();
    }

    function initAdvancedManagement() {
        // إعداد التبويبات
        const mgmtTabBtns = document.querySelectorAll('.mgmt-tab-btn');
        const mgmtTabContents = document.querySelectorAll('.mgmt-tab-content');

        mgmtTabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                mgmtTabBtns.forEach(b => b.classList.remove('active'));
                mgmtTabContents.forEach(c => c.classList.remove('active'));

                btn.classList.add('active');
                const tabId = btn.dataset.tab;
                document.getElementById(`${tabId}-tab`).classList.add('active');
            });
        });

        // ملء القوائم المنسدلة
        populateAdvancedDropdowns();

        // إعداد النماذج
        setupAdvancedForms();

        // عرض البيانات الحالية
        displayCurriculum();
        displayInspections();
        displayAdminNotes();
        displayCompetitions();
    }

    function populateAdvancedDropdowns() {
        const levelSelects = [
            'curriculum-level', 'inspection-level', 'note-level', 'competition-level'
        ];

        levelSelects.forEach(selectId => {
            const select = document.getElementById(selectId);
            if (select) {
                select.innerHTML = '<option value="">اختر المستوى</option>';
                ACADEMIC_LEVELS.forEach(level => {
                    const option = document.createElement('option');
                    option.value = level;
                    option.textContent = level;
                    select.appendChild(option);
                });
            }
        });

        const subjectSelects = [
            'curriculum-subject', 'inspection-subject', 'competition-subject'
        ];

        subjectSelects.forEach(selectId => {
            const select = document.getElementById(selectId);
            if (select) {
                select.innerHTML = '<option value="">اختر المادة</option>';
                ALL_SUBJECTS.forEach(subject => {
                    const option = document.createElement('option');
                    option.value = subject;
                    option.textContent = subject;
                    select.appendChild(option);
                });
            }
        });

        // تحديث قائمة الأفواج عند تغيير المستوى
        const noteLevelSelect = document.getElementById('note-level');
        const noteGroupSelect = document.getElementById('note-group');

        if (noteLevelSelect && noteGroupSelect) {
            noteLevelSelect.addEventListener('change', () => {
                noteGroupSelect.innerHTML = '<option value="">اختر الفوج</option>';
                const filteredGroups = groups.filter(g => g.level === noteLevelSelect.value);
                filteredGroups.forEach(group => {
                    const option = document.createElement('option');
                    option.value = group.name;
                    option.textContent = group.name;
                    noteGroupSelect.appendChild(option);
                });
            });
        }
    }

    function setupAdvancedForms() {
        // نموذج المقررات الدراسية
        const curriculumForm = document.getElementById('add-curriculum-form');
        if (curriculumForm) {
            curriculumForm.addEventListener('submit', (e) => {
                e.preventDefault();
                addCurriculumItem();
            });
        }

        // نموذج زيارات المفتش
        const inspectionForm = document.getElementById('add-inspection-form');
        if (inspectionForm) {
            inspectionForm.addEventListener('submit', (e) => {
                e.preventDefault();
                addInspection();
            });
        }

        // نموذج الملاحظات الإدارية
        const noteForm = document.getElementById('add-note-form');
        if (noteForm) {
            noteForm.addEventListener('submit', (e) => {
                e.preventDefault();
                addAdminNote();
            });
        }

        // نموذج المسابقات التربوية
        const competitionForm = document.getElementById('add-competition-form');
        if (competitionForm) {
            competitionForm.addEventListener('submit', (e) => {
                e.preventDefault();
                addCompetition();
            });
        }
    }

    function addCurriculumItem() {
        const newItem = {
            id: Date.now().toString(),
            level: document.getElementById('curriculum-level').value,
            subject: document.getElementById('curriculum-subject').value,
            topic: document.getElementById('curriculum-topic').value,
            date: document.getElementById('curriculum-date').value,
            notes: document.getElementById('curriculum-notes').value,
            createdAt: new Date().toISOString()
        };

        curriculum.push(newItem);
        saveToStorage('sbea_curriculum', curriculum);
        displayCurriculum();
        document.getElementById('add-curriculum-form').reset();
        alert('تم إضافة المقرر بنجاح');
    }

    function addInspection() {
        const newInspection = {
            id: Date.now().toString(),
            inspectorName: document.getElementById('inspector-name').value,
            level: document.getElementById('inspection-level').value,
            subject: document.getElementById('inspection-subject').value,
            date: document.getElementById('inspection-date').value,
            time: document.getElementById('inspection-time').value,
            notes: document.getElementById('inspection-notes').value,
            rating: document.getElementById('inspection-rating').value,
            createdAt: new Date().toISOString()
        };

        inspections.push(newInspection);
        saveToStorage('sbea_inspections', inspections);
        displayInspections();
        document.getElementById('add-inspection-form').reset();
        alert('تم إضافة زيارة المفتش بنجاح');
    }

    function addAdminNote() {
        const newNote = {
            id: Date.now().toString(),
            level: document.getElementById('note-level').value,
            group: document.getElementById('note-group').value,
            type: document.getElementById('note-type').value,
            content: document.getElementById('note-content').value,
            date: document.getElementById('note-date').value,
            createdAt: new Date().toISOString()
        };

        adminNotes.push(newNote);
        saveToStorage('sbea_admin_notes', adminNotes);
        displayAdminNotes();
        document.getElementById('add-note-form').reset();
        alert('تم إضافة الملاحظة بنجاح');
    }

    function addCompetition() {
        const newCompetition = {
            id: Date.now().toString(),
            name: document.getElementById('competition-name').value,
            level: document.getElementById('competition-level').value,
            subject: document.getElementById('competition-subject').value,
            date: document.getElementById('competition-date').value,
            time: document.getElementById('competition-time').value,
            description: document.getElementById('competition-description').value,
            prize: document.getElementById('competition-prize').value,
            createdAt: new Date().toISOString()
        };

        competitions.push(newCompetition);
        saveToStorage('sbea_competitions', competitions);
        displayCompetitions();
        document.getElementById('add-competition-form').reset();
        alert('تم إضافة المسابقة بنجاح');
    }

    function displayCurriculum() {
        const curriculumList = document.getElementById('curriculum-list');
        if (!curriculumList) return;

        if (curriculum.length === 0) {
            curriculumList.innerHTML = '<p class="no-data">لا توجد مقررات دراسية مسجلة</p>';
            return;
        }

        let html = '<div class="data-table"><table><thead><tr><th>المستوى</th><th>المادة</th><th>الموضوع</th><th>التاريخ</th><th>الملاحظات</th><th>الإجراءات</th></tr></thead><tbody>';

        curriculum.forEach(item => {
            html += `
                <tr>
                    <td>${item.level}</td>
                    <td>${item.subject}</td>
                    <td>${item.topic}</td>
                    <td>${new Date(item.date).toLocaleDateString('ar-DZ')}</td>
                    <td>${item.notes || '-'}</td>
                    <td>
                        <button class="delete-btn" onclick="deleteCurriculumItem('${item.id}')">حذف</button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        curriculumList.innerHTML = html;
    }

    function displayInspections() {
        const inspectionsList = document.getElementById('inspections-list');
        if (!inspectionsList) return;

        if (inspections.length === 0) {
            inspectionsList.innerHTML = '<p class="no-data">لا توجد زيارات مفتش مسجلة</p>';
            return;
        }

        let html = '<div class="data-table"><table><thead><tr><th>المفتش</th><th>المستوى</th><th>المادة</th><th>التاريخ</th><th>الوقت</th><th>التقييم</th><th>الملاحظات</th><th>الإجراءات</th></tr></thead><tbody>';

        inspections.forEach(inspection => {
            html += `
                <tr>
                    <td>${inspection.inspectorName}</td>
                    <td>${inspection.level}</td>
                    <td>${inspection.subject}</td>
                    <td>${new Date(inspection.date).toLocaleDateString('ar-DZ')}</td>
                    <td>${inspection.time}</td>
                    <td class="rating-${inspection.rating.replace(' ', '-')}">${inspection.rating}</td>
                    <td>${inspection.notes || '-'}</td>
                    <td>
                        <button class="delete-btn" onclick="deleteInspection('${inspection.id}')">حذف</button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        inspectionsList.innerHTML = html;
    }

    function displayAdminNotes() {
        const notesList = document.getElementById('notes-list');
        if (!notesList) return;

        if (adminNotes.length === 0) {
            notesList.innerHTML = '<p class="no-data">لا توجد ملاحظات إدارية مسجلة</p>';
            return;
        }

        let html = '<div class="data-table"><table><thead><tr><th>المستوى</th><th>الفوج</th><th>النوع</th><th>المحتوى</th><th>التاريخ</th><th>الإجراءات</th></tr></thead><tbody>';

        adminNotes.forEach(note => {
            html += `
                <tr>
                    <td>${note.level}</td>
                    <td>${note.group}</td>
                    <td class="note-type-${note.type}">${note.type}</td>
                    <td>${note.content}</td>
                    <td>${new Date(note.date).toLocaleDateString('ar-DZ')}</td>
                    <td>
                        <button class="delete-btn" onclick="deleteAdminNote('${note.id}')">حذف</button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        notesList.innerHTML = html;
    }

    function displayCompetitions() {
        const competitionsList = document.getElementById('competitions-list');
        if (!competitionsList) return;

        if (competitions.length === 0) {
            competitionsList.innerHTML = '<p class="no-data">لا توجد مسابقات تربوية مسجلة</p>';
            return;
        }

        let html = '<div class="data-table"><table><thead><tr><th>اسم المسابقة</th><th>المستوى</th><th>المادة</th><th>التاريخ</th><th>الوقت</th><th>الوصف</th><th>الجائزة</th><th>الإجراءات</th></tr></thead><tbody>';

        competitions.forEach(competition => {
            html += `
                <tr>
                    <td>${competition.name}</td>
                    <td>${competition.level}</td>
                    <td>${competition.subject}</td>
                    <td>${new Date(competition.date).toLocaleDateString('ar-DZ')}</td>
                    <td>${competition.time}</td>
                    <td>${competition.description || '-'}</td>
                    <td>${competition.prize || '-'}</td>
                    <td>
                        <button class="delete-btn" onclick="deleteCompetition('${competition.id}')">حذف</button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        competitionsList.innerHTML = html;
    }

    // دوال الحذف
    window.deleteCurriculumItem = function(id) {
        if (confirm('هل أنت متأكد من حذف هذا المقرر؟')) {
            curriculum = curriculum.filter(item => item.id !== id);
            saveToStorage('sbea_curriculum', curriculum);
            displayCurriculum();
        }
    };

    window.deleteInspection = function(id) {
        if (confirm('هل أنت متأكد من حذف زيارة المفتش هذه؟')) {
            inspections = inspections.filter(item => item.id !== id);
            saveToStorage('sbea_inspections', inspections);
            displayInspections();
        }
    };

    window.deleteAdminNote = function(id) {
        if (confirm('هل أنت متأكد من حذف هذه الملاحظة؟')) {
            adminNotes = adminNotes.filter(item => item.id !== id);
            saveToStorage('sbea_admin_notes', adminNotes);
            displayAdminNotes();
        }
    };

    window.deleteCompetition = function(id) {
        if (confirm('هل أنت متأكد من حذف هذه المسابقة؟')) {
            competitions = competitions.filter(item => item.id !== id);
            saveToStorage('sbea_competitions', competitions);
            displayCompetitions();
        }
    };

    // =================================================================
    //                      Activities Page
    // =================================================================
    function initActivitiesPage() {
        const activityForm = document.getElementById('add-activity-form');
        const activitiesList = document.getElementById('activities-list');

        function renderActivities() {
            activitiesList.innerHTML = '';
            activities.forEach((activity, index) => {
                const card = document.createElement('div');
                card.className = 'activity-card';
                const mediaHTML = activity.media.map(src => {
                    if (src.startsWith('data:image')) {
                        return `<img src="${src}" alt="Activity media">`;
                    } else if (src.startsWith('data:video')) {
                        return `<video controls src="${src}"></video>`;
                    }
                    return '';
                }).join('');

                card.innerHTML = `
                    <h3>${activity.name}</h3>
                    <p><strong>الفئة المستهدفة:</strong> ${activity.target}</p>
                    <p><strong>المكان:</strong> ${activity.location}</p>
                    <p><strong>التوقيت:</strong> ${new Date(activity.datetime).toLocaleString('ar-DZ')}</p>
                    <div class="media-container">${mediaHTML}</div>
                    <button class="delete-btn">حذف النشاط</button>
                `;
                card.querySelector('.delete-btn').addEventListener('click', () => {
                    activities.splice(index, 1);
                    saveToStorage('sbea_activities', activities);
                    renderActivities();
                });
                activitiesList.appendChild(card);
            });
        }

        activityForm.addEventListener('submit', e => {
            e.preventDefault();
            const mediaInput = document.getElementById('activity-media');
            const mediaFiles = Array.from(mediaInput.files);
            const mediaPromises = mediaFiles.map(file => {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = () => resolve(reader.result);
                    reader.onerror = reject;
                    reader.readAsDataURL(file);
                });
            });

            Promise.all(mediaPromises).then(mediaDataUrls => {
                const newActivity = {
                    name: document.getElementById('activity-name').value,
                    target: document.getElementById('activity-target').value,
                    location: document.getElementById('activity-location').value,
                    datetime: document.getElementById('activity-datetime').value,
                    media: mediaDataUrls
                };
                activities.push(newActivity);
                saveToStorage('sbea_activities', activities);
                renderActivities();
                activityForm.reset();
            });
        });

        renderActivities();
    }

    // =================================================================
    //                      Settings Page
    // =================================================================
    function initSettingsPage() {
        const backupBtn = document.getElementById('backup-btn');
        const restoreInput = document.getElementById('restore-input');

        backupBtn.addEventListener('click', () => {
            const data = {
                students: getFromStorage('sbea_students'),
                groups: getFromStorage('sbea_groups'),
                teachers: getFromStorage('sbea_teachers'),
                activities: getFromStorage('sbea_activities')
            };
            const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(data));
            const dlAnchorElem = document.createElement('a');
            dlAnchorElem.setAttribute("href", dataStr);
            dlAnchorElem.setAttribute("download", `sbea_backup_${new Date().toISOString().split('T')[0]}.json`);
            dlAnchorElem.click();
        });

        restoreInput.addEventListener('change', e => {
            const file = e.target.files[0];
            if (!file) return;
            const reader = new FileReader();
            reader.onload = (event) => {
                try {
                    const data = JSON.parse(event.target.result);
                    if (data.students && data.groups && data.teachers) {
                        saveToStorage('sbea_students', data.students);
                        saveToStorage('sbea_groups', data.groups);
                        saveToStorage('sbea_teachers', data.teachers);
                        saveToStorage('sbea_activities', data.activities || []);
                        alert('تم استعادة البيانات بنجاح! قم بتحديث الصفحة لرؤية التغييرات.');
                    } else {
                        alert('ملف النسخ الاحتياطي غير صالح.');
                    }
                } catch (error) {
                    alert('خطأ في قراءة الملف.');
                }
            };
            reader.readAsText(file);
            restoreInput.value = '';
        });
    }

    // =================================================================
    //                      Staff Page
    // =================================================================
    function initStaffPage() {
        // إعداد التبويبات
        const staffTabBtns = document.querySelectorAll('.staff-tab-btn');
        const staffTabContents = document.querySelectorAll('.staff-tab-content');

        staffTabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                staffTabBtns.forEach(b => b.classList.remove('active'));
                staffTabContents.forEach(c => c.classList.remove('active'));

                btn.classList.add('active');
                const tabId = btn.dataset.tab;
                document.getElementById(`${tabId}-tab`).classList.add('active');
            });
        });

        // إعداد النماذج
        setupStaffForms();

        // عرض البيانات
        displayAdminStaff();
        displayGuards();
        displayCleaners();
        displayTeacherSalaries();

        // إعداد النوافذ المنبثقة
        initStaffModals();
    }

    function setupStaffForms() {
        // نموذج الموظفين الإداريين
        const adminForm = document.getElementById('add-admin-form');
        if (adminForm) {
            adminForm.addEventListener('submit', (e) => {
                e.preventDefault();
                addAdminStaff();
            });
        }

        // نموذج الحراس
        const guardForm = document.getElementById('add-guard-form');
        if (guardForm) {
            guardForm.addEventListener('submit', (e) => {
                e.preventDefault();
                addGuard();
            });
        }

        // نموذج مسؤولي النظافة
        const cleanerForm = document.getElementById('add-cleaner-form');
        if (cleanerForm) {
            cleanerForm.addEventListener('submit', (e) => {
                e.preventDefault();
                addCleaner();
            });
        }
    }

    function addAdminStaff() {
        const newStaff = {
            id: Date.now().toString(),
            name: document.getElementById('admin-name').value.trim(),
            position: document.getElementById('admin-position').value.trim(),
            salary: parseFloat(document.getElementById('admin-salary').value),
            phone: document.getElementById('admin-phone').value.trim(),
            email: document.getElementById('admin-email').value.trim(),
            hireDate: document.getElementById('admin-hire-date').value,
            monthlySalaries: createMonthlySalaries(parseFloat(document.getElementById('admin-salary').value)),
            createdAt: new Date().toISOString()
        };

        adminStaff.push(newStaff);
        saveToStorage('sbea_admin_staff', adminStaff);
        displayAdminStaff();
        document.getElementById('add-admin-form').reset();
        alert('تم إضافة الموظف الإداري بنجاح');
    }

    function addGuard() {
        const newGuard = {
            id: Date.now().toString(),
            name: document.getElementById('guard-name').value.trim(),
            shift: document.getElementById('guard-shift').value,
            salary: parseFloat(document.getElementById('guard-salary').value),
            phone: document.getElementById('guard-phone').value.trim(),
            hireDate: document.getElementById('guard-hire-date').value,
            monthlySalaries: createMonthlySalaries(parseFloat(document.getElementById('guard-salary').value)),
            createdAt: new Date().toISOString()
        };

        guards.push(newGuard);
        saveToStorage('sbea_guards', guards);
        displayGuards();
        document.getElementById('add-guard-form').reset();
        alert('تم إضافة الحارس بنجاح');
    }

    function addCleaner() {
        const newCleaner = {
            id: Date.now().toString(),
            name: document.getElementById('cleaner-name').value.trim(),
            area: document.getElementById('cleaner-area').value,
            salary: parseFloat(document.getElementById('cleaner-salary').value),
            phone: document.getElementById('cleaner-phone').value.trim(),
            hireDate: document.getElementById('cleaner-hire-date').value,
            monthlySalaries: createMonthlySalaries(parseFloat(document.getElementById('cleaner-salary').value)),
            createdAt: new Date().toISOString()
        };

        cleaners.push(newCleaner);
        saveToStorage('sbea_cleaners', cleaners);
        displayCleaners();
        document.getElementById('add-cleaner-form').reset();
        alert('تم إضافة مسؤول النظافة بنجاح');
    }

    function createMonthlySalaries(monthlySalary) {
        const salaries = {};
        ACADEMIC_MONTHS.forEach(month => {
            salaries[month] = {
                status: 'غير مدفوع',
                amount: 0,
                remaining: monthlySalary,
                dueAmount: monthlySalary
            };
        });
        return salaries;
    }

    function displayAdminStaff() {
        const adminList = document.getElementById('admin-list');
        if (!adminList) return;

        if (adminStaff.length === 0) {
            adminList.innerHTML = '<p class="no-data">لا يوجد موظفون إداريون مسجلون</p>';
            return;
        }

        let html = '<div class="staff-table"><table><thead><tr><th>الاسم</th><th>المنصب</th><th>الراتب الشهري</th><th>الهاتف</th><th>البريد الإلكتروني</th><th>تاريخ التوظيف</th><th>الإجراءات</th></tr></thead><tbody>';

        adminStaff.forEach(staff => {
            html += `
                <tr>
                    <td>${staff.name}</td>
                    <td>${staff.position}</td>
                    <td>${staff.salary.toLocaleString()} DHS</td>
                    <td>${staff.phone}</td>
                    <td>${staff.email || '-'}</td>
                    <td>${new Date(staff.hireDate).toLocaleDateString('ar-DZ')}</td>
                    <td>
                        <button class="salary-btn" onclick="openSalaryModal('${staff.id}', 'admin')">الراتب</button>
                        <button class="delete-btn" onclick="deleteAdminStaff('${staff.id}')">حذف</button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        adminList.innerHTML = html;
    }

    function displayGuards() {
        const guardsList = document.getElementById('guards-list');
        if (!guardsList) return;

        if (guards.length === 0) {
            guardsList.innerHTML = '<p class="no-data">لا يوجد حراس مسجلون</p>';
            return;
        }

        let html = '<div class="staff-table"><table><thead><tr><th>الاسم</th><th>الوردية</th><th>الراتب الشهري</th><th>الهاتف</th><th>تاريخ التوظيف</th><th>الإجراءات</th></tr></thead><tbody>';

        guards.forEach(guard => {
            html += `
                <tr>
                    <td>${guard.name}</td>
                    <td>${guard.shift}</td>
                    <td>${guard.salary.toLocaleString()} DHS</td>
                    <td>${guard.phone}</td>
                    <td>${new Date(guard.hireDate).toLocaleDateString('ar-DZ')}</td>
                    <td>
                        <button class="salary-btn" onclick="openSalaryModal('${guard.id}', 'guard')">الراتب</button>
                        <button class="delete-btn" onclick="deleteGuard('${guard.id}')">حذف</button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        guardsList.innerHTML = html;
    }

    function displayCleaners() {
        const cleanersList = document.getElementById('cleaners-list');
        if (!cleanersList) return;

        if (cleaners.length === 0) {
            cleanersList.innerHTML = '<p class="no-data">لا يوجد مسؤولو نظافة مسجلون</p>';
            return;
        }

        let html = '<div class="staff-table"><table><thead><tr><th>الاسم</th><th>المنطقة المسؤول عنها</th><th>الراتب الشهري</th><th>الهاتف</th><th>تاريخ التوظيف</th><th>الإجراءات</th></tr></thead><tbody>';

        cleaners.forEach(cleaner => {
            html += `
                <tr>
                    <td>${cleaner.name}</td>
                    <td>${cleaner.area}</td>
                    <td>${cleaner.salary.toLocaleString()} DHS</td>
                    <td>${cleaner.phone}</td>
                    <td>${new Date(cleaner.hireDate).toLocaleDateString('ar-DZ')}</td>
                    <td>
                        <button class="salary-btn" onclick="openSalaryModal('${cleaner.id}', 'cleaner')">الراتب</button>
                        <button class="delete-btn" onclick="deleteCleaner('${cleaner.id}')">حذف</button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        cleanersList.innerHTML = html;
    }

    function displayTeacherSalaries() {
        const teacherSalariesList = document.getElementById('teacher-salaries-list');
        if (!teacherSalariesList) return;

        // حساب إجمالي الرواتب
        let totalSalaries = 0;
        let paidSalaries = 0;
        let remainingSalaries = 0;

        teachers.forEach(teacher => {
            const monthlySalary = parseFloat(teacher.salary || 0);
            totalSalaries += monthlySalary * 10; // 10 أشهر

            if (teacher.monthlySalaries) {
                Object.values(teacher.monthlySalaries).forEach(salary => {
                    paidSalaries += salary.amount || 0;
                    remainingSalaries += salary.remaining || 0;
                });
            } else {
                remainingSalaries += monthlySalary * 10;
            }
        });

        // تحديث الملخص
        document.getElementById('total-teacher-salaries').textContent = totalSalaries.toLocaleString() + ' DHS';
        document.getElementById('paid-teacher-salaries').textContent = paidSalaries.toLocaleString() + ' DHS';
        document.getElementById('remaining-teacher-salaries').textContent = remainingSalaries.toLocaleString() + ' DHS';

        if (teachers.length === 0) {
            teacherSalariesList.innerHTML = '<p class="no-data">لا يوجد أساتذة مسجلون</p>';
            return;
        }

        let html = '<div class="staff-table"><table><thead><tr><th>اسم الأستاذ</th><th>الراتب الشهري</th><th>إجمالي المدفوع</th><th>إجمالي الباقي</th><th>الإجراءات</th></tr></thead><tbody>';

        teachers.forEach(teacher => {
            let teacherPaid = 0;
            let teacherRemaining = 0;

            if (teacher.monthlySalaries) {
                Object.values(teacher.monthlySalaries).forEach(salary => {
                    teacherPaid += salary.amount || 0;
                    teacherRemaining += salary.remaining || 0;
                });
            } else {
                teacherRemaining = parseFloat(teacher.salary || 0) * 10;
            }

            html += `
                <tr>
                    <td>${teacher.name}</td>
                    <td>${parseFloat(teacher.salary || 0).toLocaleString()} DHS</td>
                    <td class="paid">${teacherPaid.toLocaleString()} DHS</td>
                    <td class="${teacherRemaining > 0 ? 'unpaid' : 'paid'}">${teacherRemaining.toLocaleString()} DHS</td>
                    <td>
                        <button class="salary-btn" onclick="openTeacherSalaryModal('${teacher.name}')">إدارة الراتب</button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        teacherSalariesList.innerHTML = html;
    }

    function initStaffModals() {
        // إغلاق النوافذ المنبثقة
        document.querySelectorAll('.close-modal').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.modal').forEach(modal => {
                    modal.style.display = 'none';
                });
            });
        });
    }

    // دوال الحذف
    window.deleteAdminStaff = function(id) {
        if (confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
            adminStaff = adminStaff.filter(staff => staff.id !== id);
            saveToStorage('sbea_admin_staff', adminStaff);
            displayAdminStaff();
        }
    };

    window.deleteGuard = function(id) {
        if (confirm('هل أنت متأكد من حذف هذا الحارس؟')) {
            guards = guards.filter(guard => guard.id !== id);
            saveToStorage('sbea_guards', guards);
            displayGuards();
        }
    };

    window.deleteCleaner = function(id) {
        if (confirm('هل أنت متأكد من حذف مسؤول النظافة هذا؟')) {
            cleaners = cleaners.filter(cleaner => cleaner.id !== id);
            saveToStorage('sbea_cleaners', cleaners);
            displayCleaners();
        }
    };

    window.openSalaryModal = function(id, type) {
        // سيتم تطوير هذه الوظيفة لاحقاً
        alert('سيتم تفعيل إدارة الرواتب قريباً');
    };

    window.openTeacherSalaryModal = function(teacherName) {
        // سيتم تطوير هذه الوظيفة لاحقاً
        alert('سيتم تفعيل إدارة رواتب الأساتذة قريباً');
    };

    // =================================================================
    //                      Financial Page
    // =================================================================
    function initFinancialPage() {
        // إعداد التبويبات
        const financialTabBtns = document.querySelectorAll('.financial-tab-btn');
        const financialTabContents = document.querySelectorAll('.financial-tab-content');

        financialTabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                financialTabBtns.forEach(b => b.classList.remove('active'));
                financialTabContents.forEach(c => c.classList.remove('active'));

                btn.classList.add('active');
                const tabId = btn.dataset.tab;
                document.getElementById(`${tabId}-tab`).classList.add('active');
            });
        });

        // إعداد البحث والفلاتر
        setupFinancialFilters();

        // عرض البيانات المالية
        displayStudentPayments();
        displayTeacherSalariesFinancial();
        displayStaffSalariesFinancial();
        displayTransportFees();

        // إعداد النوافذ المنبثقة
        initFinancialModals();

        // إعداد نظام الدفع الجماعي
        initBulkPaymentSystem();
    }

    function initBulkPaymentSystem() {
        const selectAllBtn = document.getElementById('select-all-students');
        const clearSelectionBtn = document.getElementById('clear-selection');
        const bulkPaymentBtn = document.getElementById('bulk-payment-btn');
        const selectedCountEl = document.getElementById('selected-count');
        const selectedPreview = document.getElementById('selected-students-preview');

        if (!selectAllBtn || !clearSelectionBtn || !bulkPaymentBtn) return;

        // تحديث عدد التلاميذ المحددين
        function updateSelectedCount() {
            const selectedCheckboxes = document.querySelectorAll('.student-select:checked');
            const count = selectedCheckboxes.length;

            if (selectedCountEl) selectedCountEl.textContent = count;
            if (bulkPaymentBtn) bulkPaymentBtn.disabled = count === 0;

            // تحديث معاينة التلاميذ المحددين
            updateSelectedPreview(selectedCheckboxes);
        }

        // تحديث معاينة التلاميذ المحددين
        function updateSelectedPreview(checkboxes) {
            if (!selectedPreview) return;

            if (checkboxes.length === 0) {
                selectedPreview.innerHTML = '<p>لم يتم تحديد أي تلميذ بعد.</p>';
                return;
            }

            let previewHTML = '';
            checkboxes.forEach(checkbox => {
                const barcode = checkbox.value;
                const student = students.find(s => s.barcode === barcode);
                if (student) {
                    previewHTML += `
                        <div class="selected-student-item">
                            <span>${student.name} - ${student.level}</span>
                            <span>${student.barcode.padStart(6, '0')}</span>
                        </div>
                    `;
                }
            });

            selectedPreview.innerHTML = previewHTML;
        }

        // تحديد الكل
        selectAllBtn.addEventListener('click', () => {
            const checkboxes = document.querySelectorAll('.student-select');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
            updateSelectedCount();
        });

        // إلغاء التحديد
        clearSelectionBtn.addEventListener('click', () => {
            const checkboxes = document.querySelectorAll('.student-select');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            updateSelectedCount();
        });

        // فتح نافذة الدفع الجماعي
        bulkPaymentBtn.addEventListener('click', () => {
            openBulkPaymentModal();
        });

        // إضافة معالج أحداث للخانات
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('student-select')) {
                updateSelectedCount();
            }
        });

        // تحديث العدد الأولي
        updateSelectedCount();
    }

    function setupFinancialFilters() {
        const studentSearch = document.getElementById('student-search');
        const monthFilter = document.getElementById('month-filter');
        const statusFilter = document.getElementById('payment-status-filter');

        if (studentSearch) {
            studentSearch.addEventListener('input', () => {
                displayStudentPayments();
            });
        }

        if (monthFilter) {
            monthFilter.addEventListener('change', () => {
                displayStudentPayments();
            });
        }

        if (statusFilter) {
            statusFilter.addEventListener('change', () => {
                displayStudentPayments();
            });
        }
    }

    function displayStudentPayments() {
        const studentPaymentsList = document.getElementById('student-payments-list');
        if (!studentPaymentsList) return;

        const searchTerm = document.getElementById('student-search')?.value.toLowerCase() || '';
        const monthFilter = document.getElementById('month-filter')?.value || '';
        const statusFilter = document.getElementById('payment-status-filter')?.value || '';

        // حساب الإحصائيات
        let totalFees = 0;
        let totalPaid = 0;
        let totalRemaining = 0;

        // فلترة التلاميذ
        const filteredStudents = students.filter(student => {
            return student.name.toLowerCase().includes(searchTerm) ||
                   student.barcode.includes(searchTerm);
        });

        let html = `
            <div class="financial-table">
                <table>
                    <thead>
                        <tr>
                            <th>الرقم التسلسلي</th>
                            <th>اسم التلميذ</th>
                            <th>المستوى</th>
                            <th>الفوج</th>
                            <th>المستحقات الشهرية الشاملة</th>
                            <th>خدمة النقل</th>
                            <th>إجمالي المدفوع</th>
                            <th>إجمالي الباقي</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        filteredStudents.forEach(student => {
            let studentPaid = 0;
            let studentRemaining = 0;
            let studentTotal = 0;

            // حساب المستحقات الشاملة (دراسية + نقل)
            const monthlyFee = parseFloat(student.fee || 0);
            const transportFee = student.hasTransport ? parseFloat(student.transportFee || 0) : 0;
            const totalMonthlyDue = monthlyFee + transportFee;

            if (student.monthlyPayments) {
                Object.entries(student.monthlyPayments).forEach(([month, payment]) => {
                    if (!monthFilter || month === monthFilter) {
                        if (!statusFilter || payment.status === statusFilter) {
                            studentPaid += payment.amount || 0;
                            studentRemaining += payment.remaining || 0;
                            // استخدام المستحقات الشاملة
                            studentTotal += totalMonthlyDue;
                        }
                    }
                });
            } else {
                // إذا لم توجد دفعات، احسب المستحقات الكاملة لـ 10 أشهر
                studentTotal = totalMonthlyDue * 10;
                studentRemaining = studentTotal;
            }

            totalFees += studentTotal;
            totalPaid += studentPaid;
            totalRemaining += studentRemaining;

            // استخدام المتغيرات المحسوبة مسبقاً للعرض

            html += `
                <tr>
                    <td>${student.barcode.padStart(6, '0')}</td>
                    <td>${student.name}</td>
                    <td>${student.level}</td>
                    <td>${student.group}</td>
                    <td>
                        <div class="fee-breakdown">
                            <div>دراسية: ${monthlyFee.toLocaleString()} DHS</div>
                            ${student.hasTransport ? `<div>نقل: ${transportFee.toLocaleString()} DHS</div>` : ''}
                            <div class="total-fee"><strong>المجموع: ${totalMonthlyDue.toLocaleString()} DHS</strong></div>
                        </div>
                    </td>
                    <td>${student.hasTransport ? 'نعم' : 'لا'}</td>
                    <td class="paid">${studentPaid.toLocaleString()} DHS</td>
                    <td class="${studentRemaining > 0 ? 'unpaid' : 'paid'}">${studentRemaining.toLocaleString()} DHS</td>
                    <td>
                        <button class="quick-pay-btn" onclick="openQuickPayment('${student.barcode}')">دفع سريع</button>
                        <button class="view-details-btn" onclick="viewPaymentDetails('${student.barcode}')">التفاصيل</button>
                        <input type="checkbox" class="student-select" value="${student.barcode}" title="اختر للدفع الجماعي">
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        studentPaymentsList.innerHTML = html;

        // تحديث الإحصائيات
        updateFinancialSummary(totalFees, totalPaid, totalRemaining);
    }

    function updateFinancialSummary(totalFees, totalPaid, totalRemaining) {
        const totalFeesEl = document.getElementById('total-student-fees');
        const totalPaidEl = document.getElementById('total-paid-fees');
        const totalRemainingEl = document.getElementById('total-remaining-fees');
        const collectionPercentageEl = document.getElementById('collection-percentage');

        if (totalFeesEl) totalFeesEl.textContent = totalFees.toLocaleString() + ' DHS';
        if (totalPaidEl) totalPaidEl.textContent = totalPaid.toLocaleString() + ' DHS';
        if (totalRemainingEl) totalRemainingEl.textContent = totalRemaining.toLocaleString() + ' DHS';

        const percentage = totalFees > 0 ? ((totalPaid / totalFees) * 100).toFixed(1) : 0;
        if (collectionPercentageEl) collectionPercentageEl.textContent = percentage + '%';
    }

    function displayTeacherSalariesFinancial() {
        const teacherSalariesList = document.getElementById('teacher-salaries-list');
        if (!teacherSalariesList) return;

        let html = `
            <div class="financial-table">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الأستاذ</th>
                            <th>الراتب الشهري</th>
                            <th>إجمالي المدفوع</th>
                            <th>إجمالي الباقي</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        teachers.forEach(teacher => {
            let teacherPaid = 0;
            let teacherRemaining = 0;

            if (teacher.monthlySalaries) {
                Object.values(teacher.monthlySalaries).forEach(salary => {
                    teacherPaid += salary.amount || 0;
                    teacherRemaining += salary.remaining || 0;
                });
            } else {
                teacherRemaining = parseFloat(teacher.salary || 0) * 10;
            }

            html += `
                <tr>
                    <td>${teacher.name}</td>
                    <td>${parseFloat(teacher.salary || 0).toLocaleString()} DHS</td>
                    <td class="paid">${teacherPaid.toLocaleString()} DHS</td>
                    <td class="${teacherRemaining > 0 ? 'unpaid' : 'paid'}">${teacherRemaining.toLocaleString()} DHS</td>
                    <td>
                        <button class="salary-pay-btn" onclick="payTeacherSalary('${teacher.name}')">دفع راتب</button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        teacherSalariesList.innerHTML = html;
    }

    function displayStaffSalariesFinancial() {
        // سيتم تطوير هذه الوظيفة
    }

    function displayTransportFees() {
        const transportFeesList = document.getElementById('transport-fees-list');
        if (!transportFeesList) return;

        const transportStudents = students.filter(student => student.hasTransport);

        let html = `
            <div class="financial-table">
                <table>
                    <thead>
                        <tr>
                            <th>الرقم التسلسلي</th>
                            <th>اسم التلميذ</th>
                            <th>رسوم النقل الشهرية</th>
                            <th>إجمالي المدفوع</th>
                            <th>إجمالي الباقي</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        transportStudents.forEach(student => {
            // حساب رسوم النقل المدفوعة والمتبقية
            let transportPaid = 0;
            let transportRemaining = 0;
            const monthlyTransportFee = parseFloat(student.transportFee || 0);

            // هنا يمكن إضافة منطق حساب رسوم النقل المدفوعة
            transportRemaining = monthlyTransportFee * 10; // افتراضياً غير مدفوع

            html += `
                <tr>
                    <td>${student.barcode.padStart(6, '0')}</td>
                    <td>${student.name}</td>
                    <td>${monthlyTransportFee.toLocaleString()} DHS</td>
                    <td class="paid">${transportPaid.toLocaleString()} DHS</td>
                    <td class="unpaid">${transportRemaining.toLocaleString()} DHS</td>
                    <td>
                        <button class="transport-pay-btn" onclick="payTransportFee('${student.barcode}')">دفع رسوم النقل</button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        transportFeesList.innerHTML = html;
    }

    function initFinancialModals() {
        // إعداد النوافذ المنبثقة للدفع السريع
        document.querySelectorAll('.close-modal, .cancel-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.modal').forEach(modal => {
                    modal.style.display = 'none';
                });
            });
        });

        // إعداد زر إنشاء وصل الدفع
        const generateReceiptBtn = document.getElementById('generate-receipt-btn');
        if (generateReceiptBtn) {
            generateReceiptBtn.addEventListener('click', () => {
                window.generatePaymentReceipt();
            });
        }
    }

    // دوال الدفع السريع
    window.openQuickPayment = function(barcode) {
        const student = students.find(s => s.barcode === barcode);
        if (!student) return;

        const modal = document.getElementById('quick-payment-modal');
        const paymentInfo = document.getElementById('quick-payment-info');
        const monthSelect = document.getElementById('payment-month');
        const form = document.getElementById('quick-payment-form');

        // حساب المستحقات الشاملة
        const monthlyFee = parseFloat(student.fee || 0);
        const transportFee = student.hasTransport ? parseFloat(student.transportFee || 0) : 0;
        const totalMonthlyDue = monthlyFee + transportFee;

        paymentInfo.innerHTML = `
            <div class="student-payment-info">
                <h4>${student.name} - ${student.level} - ${student.group}</h4>
                <div class="fee-details">
                    <div>المستحقات الدراسية: ${monthlyFee.toLocaleString()} DHS</div>
                    ${student.hasTransport ? `<div>رسوم النقل: ${transportFee.toLocaleString()} DHS</div>` : ''}
                    <div class="total-due"><strong>إجمالي المستحقات الشهرية: ${totalMonthlyDue.toLocaleString()} DHS</strong></div>
                </div>
            </div>
        `;

        // ملء قائمة الأشهر
        monthSelect.innerHTML = '<option value="">اختر الشهر</option>';
        ACADEMIC_MONTHS.forEach(month => {
            const option = document.createElement('option');
            option.value = month;
            option.textContent = month;
            monthSelect.appendChild(option);
        });

        // إعداد معالج النموذج
        form.onsubmit = (e) => {
            e.preventDefault();
            processQuickPayment(student, totalMonthlyDue);
        };

        modal.style.display = 'block';
    };

    function processQuickPayment(student, totalMonthlyDue) {
        const month = document.getElementById('payment-month').value;
        const amount = parseFloat(document.getElementById('payment-amount').value);
        const notes = document.getElementById('payment-notes').value;

        if (!month) {
            alert('يرجى اختيار الشهر.');
            return;
        }

        if (!amount || amount <= 0) {
            alert('يرجى إدخال مبلغ صحيح.');
            return;
        }

        if (amount > totalMonthlyDue) {
            alert(`المبلغ المدخل أكبر من المستحقات الشهرية (${totalMonthlyDue.toLocaleString()} DHS).`);
            return;
        }

        // تحديث دفعات التلميذ
        if (!student.monthlyPayments) {
            student.monthlyPayments = createMonthlyPayments(parseFloat(student.fee || 0));
        }

        const payment = student.monthlyPayments[month] || {
            status: 'غير مدفوع',
            amount: 0,
            remaining: totalMonthlyDue,
            dueAmount: totalMonthlyDue
        };

        payment.amount += amount;
        payment.remaining = Math.max(0, totalMonthlyDue - payment.amount);
        payment.status = payment.remaining > 0 ? 'جزئي' : 'مدفوع';
        payment.dueAmount = totalMonthlyDue;

        if (notes) {
            payment.notes = notes;
        }

        student.monthlyPayments[month] = payment;

        // حفظ البيانات
        saveToStorage('sbea_students', students);

        // تحديث العرض
        displayStudentPayments();

        // إغلاق النافذة
        document.getElementById('quick-payment-modal').style.display = 'none';

        // إعادة تعيين النموذج
        document.getElementById('quick-payment-form').reset();

        alert(`تم دفع ${amount.toLocaleString()} DHS لـ ${student.name} عن شهر ${month}. الباقي: ${payment.remaining.toLocaleString()} DHS`);
    }

    window.viewPaymentDetails = function(barcode) {
        // فتح نافذة تفاصيل الدفعات (نفس النافذة الموجودة في صفحة التلاميذ)
        const student = students.find(s => s.barcode === barcode);
        if (student) {
            openPaymentsModal(student);
        }
    };

    window.payTeacherSalary = function(teacherName) {
        alert(`سيتم تفعيل دفع راتب ${teacherName} قريباً`);
    };

    window.payTransportFee = function(barcode) {
        alert('سيتم تفعيل دفع رسوم النقل قريباً');
    };

    // دالة فتح نافذة الدفع الجماعي
    function openBulkPaymentModal() {
        const selectedCheckboxes = document.querySelectorAll('.student-select:checked');
        if (selectedCheckboxes.length === 0) {
            alert('يرجى تحديد تلميذ واحد على الأقل للدفع الجماعي.');
            return;
        }

        const modal = document.getElementById('bulk-payment-modal');
        const studentsList = document.getElementById('bulk-students-list');
        const studentsCount = document.getElementById('bulk-students-count');

        if (!modal || !studentsList || !studentsCount) return;

        // عرض قائمة التلاميذ المحددين
        let studentsHTML = '';
        let totalAmount = 0;

        selectedCheckboxes.forEach(checkbox => {
            const barcode = checkbox.value;
            const student = students.find(s => s.barcode === barcode);
            if (student) {
                const monthlyFee = parseFloat(student.fee || 0);
                const transportFee = student.hasTransport ? parseFloat(student.transportFee || 0) : 0;
                const totalMonthlyDue = monthlyFee + transportFee;

                studentsHTML += `
                    <div class="bulk-student-item" data-barcode="${barcode}">
                        <div class="student-info">
                            <strong>${student.name}</strong>
                            <span>${student.level} - ${student.group}</span>
                            <span>الرقم: ${student.barcode.padStart(6, '0')}</span>
                        </div>
                        <div class="student-fees">
                            <div>دراسية: ${monthlyFee.toLocaleString()} DHS</div>
                            ${student.hasTransport ? `<div>نقل: ${transportFee.toLocaleString()} DHS</div>` : ''}
                            <div><strong>المجموع: ${totalMonthlyDue.toLocaleString()} DHS</strong></div>
                        </div>
                    </div>
                `;
                totalAmount += totalMonthlyDue;
            }
        });

        studentsList.innerHTML = studentsHTML;
        studentsCount.textContent = selectedCheckboxes.length;

        // إعداد نموذج الدفع الجماعي
        setupBulkPaymentForm(selectedCheckboxes, totalAmount);

        modal.style.display = 'block';
    }

    function setupBulkPaymentForm(selectedCheckboxes, totalAmount) {
        const form = document.getElementById('bulk-payment-form');
        const paymentType = document.getElementById('bulk-payment-type');
        const amountContainer = document.getElementById('bulk-amount-container');
        const totalAmountEl = document.getElementById('bulk-total-amount');

        if (!form || !paymentType || !amountContainer || !totalAmountEl) return;

        // تحديث المبلغ الإجمالي
        totalAmountEl.textContent = totalAmount.toLocaleString() + ' DHS';

        // إظهار/إخفاء حقل المبلغ حسب نوع الدفع
        paymentType.addEventListener('change', () => {
            if (paymentType.value === 'partial') {
                amountContainer.style.display = 'block';
            } else {
                amountContainer.style.display = 'none';
            }
        });

        // إزالة معالج سابق إن وجد
        form.onsubmit = null;

        // معالج إرسال النموذج
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            console.log('تم إرسال نموذج الدفع الجماعي');
            processBulkPayment(selectedCheckboxes);
        });
    }

    function processBulkPayment(selectedCheckboxes) {
        const month = document.getElementById('bulk-payment-month').value;
        const paymentType = document.getElementById('bulk-payment-type').value;
        const amount = document.getElementById('bulk-payment-amount').value;
        const notes = document.getElementById('bulk-payment-notes').value;

        if (!month || !paymentType) {
            alert('يرجى ملء جميع الحقول المطلوبة.');
            return;
        }

        if (paymentType === 'partial' && (!amount || parseFloat(amount) <= 0)) {
            alert('يرجى إدخال مبلغ صحيح للدفع الجزئي.');
            return;
        }

        let processedCount = 0;
        let totalProcessed = 0;

        selectedCheckboxes.forEach(checkbox => {
            const barcode = checkbox.value;
            const student = students.find(s => s.barcode === barcode);
            if (student) {
                const monthlyFee = parseFloat(student.fee || 0);
                const transportFee = student.hasTransport ? parseFloat(student.transportFee || 0) : 0;
                const totalMonthlyDue = monthlyFee + transportFee;

                const paymentAmount = paymentType === 'full' ? totalMonthlyDue : parseFloat(amount);

                // تحديث دفعات التلميذ
                if (!student.monthlyPayments) {
                    student.monthlyPayments = createMonthlyPayments(monthlyFee);
                }

                const payment = student.monthlyPayments[month] || {
                    status: 'غير مدفوع',
                    amount: 0,
                    remaining: totalMonthlyDue,
                    dueAmount: totalMonthlyDue
                };

                payment.amount += paymentAmount;
                payment.remaining = Math.max(0, totalMonthlyDue - payment.amount);
                payment.status = payment.remaining > 0 ? 'جزئي' : 'مدفوع';
                payment.dueAmount = totalMonthlyDue;

                if (notes) {
                    payment.notes = notes;
                }

                student.monthlyPayments[month] = payment;
                processedCount++;
                totalProcessed += paymentAmount;
            }
        });

        // حفظ البيانات
        saveToStorage('sbea_students', students);

        // تحديث العرض في جميع الصفحات
        displayStudentPayments();

        // تحديث الصفحة الرئيسية إذا كانت مفتوحة
        if (typeof renderStudents === 'function') {
            renderStudents();
        }

        // تحديث الإحصائيات
        if (typeof updateDashboardStats === 'function') {
            updateDashboardStats();
        }

        // إغلاق النافذة
        document.getElementById('bulk-payment-modal').style.display = 'none';

        // إلغاء تحديد التلاميذ
        selectedCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });

        // تحديث عداد التحديد
        const selectedCountEl = document.getElementById('selected-count');
        if (selectedCountEl) selectedCountEl.textContent = '0';

        const bulkPaymentBtn = document.getElementById('bulk-payment-btn');
        if (bulkPaymentBtn) bulkPaymentBtn.disabled = true;

        alert(`تم دفع مستحقات ${processedCount} تلميذ بإجمالي ${totalProcessed.toLocaleString()} DHS لشهر ${month}`);
    }

    // إضافة دالة إنشاء وصل الدفع
    window.generatePaymentReceipt = function() {
        const selectedCheckboxes = document.querySelectorAll('.student-select:checked');
        if (selectedCheckboxes.length === 0) {
            alert('يرجى تحديد تلميذ واحد على الأقل لإنشاء وصل الدفع.');
            return;
        }

        const month = document.getElementById('bulk-payment-month').value;
        if (!month) {
            alert('يرجى اختيار الشهر أولاً.');
            return;
        }

        // إنشاء وصل الدفع
        createPaymentReceipt(selectedCheckboxes, month);
    };

    function createPaymentReceipt(selectedCheckboxes, month) {
        const currentDate = new Date().toLocaleDateString('ar-SA');
        let receiptHTML = `
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>وصل دفع - مؤسسة النور التربوي</title>
                <style>
                    body { font-family: 'Arial', sans-serif; margin: 20px; }
                    .header { text-align: center; border-bottom: 2px solid #007bff; padding-bottom: 20px; margin-bottom: 20px; }
                    .logo { width: 80px; height: 80px; margin: 0 auto 10px; }
                    .school-name { color: #007bff; font-size: 24px; font-weight: bold; margin: 10px 0; }
                    .receipt-title { font-size: 20px; color: #333; margin: 10px 0; }
                    .receipt-info { display: flex; justify-content: space-between; margin: 20px 0; }
                    .students-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                    .students-table th, .students-table td { border: 1px solid #ddd; padding: 10px; text-align: center; }
                    .students-table th { background-color: #007bff; color: white; }
                    .total-section { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
                    .qr-code { text-align: center; margin: 20px 0; }
                    .footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }
                    @media print { body { margin: 0; } }
                </style>
            </head>
            <body>
                <div class="header">
                    <img src="logo" alt="شعار المؤسسة" class="logo">
                    <div class="school-name">مؤسسة النور التربوي للتعليم الخصوصي</div>
                    <div class="receipt-title">وصل دفع المستحقات الشهرية</div>
                </div>

                <div class="receipt-info">
                    <div><strong>التاريخ:</strong> ${currentDate}</div>
                    <div><strong>الشهر:</strong> ${month}</div>
                    <div><strong>رقم الوصل:</strong> ${Date.now()}</div>
                </div>

                <table class="students-table">
                    <thead>
                        <tr>
                            <th>الرقم التسلسلي</th>
                            <th>اسم التلميذ</th>
                            <th>المستوى</th>
                            <th>الفوج</th>
                            <th>المستحقات الدراسية</th>
                            <th>رسوم النقل</th>
                            <th>المجموع</th>
                            <th>الرمز الشريطي</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        let totalAmount = 0;
        let studentsCount = 0;

        selectedCheckboxes.forEach(checkbox => {
            const barcode = checkbox.value;
            const student = students.find(s => s.barcode === barcode);
            if (student) {
                const monthlyFee = parseFloat(student.fee || 0);
                const transportFee = student.hasTransport ? parseFloat(student.transportFee || 0) : 0;
                const totalMonthlyDue = monthlyFee + transportFee;

                receiptHTML += `
                    <tr>
                        <td>${student.barcode.padStart(6, '0')}</td>
                        <td>${student.name}</td>
                        <td>${student.level}</td>
                        <td>${student.group}</td>
                        <td>${monthlyFee.toLocaleString()} DHS</td>
                        <td>${transportFee > 0 ? transportFee.toLocaleString() + ' DHS' : 'لا يستفيد'}</td>
                        <td><strong>${totalMonthlyDue.toLocaleString()} DHS</strong></td>
                        <td>||||| ${student.barcode.padStart(6, '0')} |||||</td>
                    </tr>
                `;

                totalAmount += totalMonthlyDue;
                studentsCount++;
            }
        });

        receiptHTML += `
                    </tbody>
                </table>

                <div class="total-section">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <strong>عدد التلاميذ:</strong> ${studentsCount}
                        </div>
                        <div style="font-size: 18px;">
                            <strong>إجمالي المبلغ: ${totalAmount.toLocaleString()} DHS</strong>
                        </div>
                    </div>
                </div>

                <div class="qr-code">
                    <div style="border: 2px solid #007bff; padding: 20px; display: inline-block;">
                        <div>رمز QR للتحقق</div>
                        <div style="font-family: monospace; font-size: 12px; margin-top: 10px;">
                            ${btoa(`SBEA-${Date.now()}-${month}-${studentsCount}-${totalAmount}`)}
                        </div>
                    </div>
                </div>

                <div class="footer">
                    <p>مؤسسة النور التربوي للتعليم الخصوصي - نظام إدارة المؤسسات التعليمية SBEA</p>
                    <p>تم إنشاء هذا الوصل تلقائياً في ${currentDate}</p>
                </div>
            </body>
            </html>
        `;

        // فتح نافذة جديدة وطباعة الوصل
        const printWindow = window.open('', '_blank');
        printWindow.document.write(receiptHTML);
        printWindow.document.close();
        printWindow.focus();

        // طباعة تلقائية
        setTimeout(() => {
            printWindow.print();
        }, 500);
    }

    // دالة عرض المصاريف السنوية
    function displayAnnualFees(student) {
        // تحديث مبالغ المصاريف السنوية
        const registrationFeeAmount = document.getElementById('registration-fee-amount');
        const booksFeeAmount = document.getElementById('books-fee-amount');
        const registrationFeeStatus = document.getElementById('registration-fee-status');
        const booksFeeStatus = document.getElementById('books-fee-status');

        if (!student.annualFees) {
            student.annualFees = {
                registration: 0,
                books: 0,
                registrationPaid: false,
                booksPaid: false
            };
        }

        if (registrationFeeAmount) {
            registrationFeeAmount.textContent = student.annualFees.registration.toLocaleString() + ' DHS';
        }

        if (booksFeeAmount) {
            booksFeeAmount.textContent = student.annualFees.books.toLocaleString() + ' DHS';
        }

        if (registrationFeeStatus) {
            registrationFeeStatus.textContent = student.annualFees.registrationPaid ? 'مدفوع' : 'غير مدفوع';
            registrationFeeStatus.className = `fee-status ${student.annualFees.registrationPaid ? 'paid' : 'unpaid'}`;
        }

        if (booksFeeStatus) {
            booksFeeStatus.textContent = student.annualFees.booksPaid ? 'مدفوع' : 'غير مدفوع';
            booksFeeStatus.className = `fee-status ${student.annualFees.booksPaid ? 'paid' : 'unpaid'}`;
        }

        // إعداد أزرار الدفع
        const payButtons = document.querySelectorAll('.pay-annual-fee-btn');
        payButtons.forEach(button => {
            const feeType = button.dataset.type;
            const isPaid = feeType === 'registration' ? student.annualFees.registrationPaid : student.annualFees.booksPaid;
            const amount = feeType === 'registration' ? student.annualFees.registration : student.annualFees.books;

            button.disabled = isPaid || amount <= 0;
            button.textContent = isPaid ? 'مدفوع' : (amount <= 0 ? 'غير محدد' : `دفع ${feeType === 'registration' ? 'مصاريف التسجيل' : 'مصاريف الكتب'}`);

            button.onclick = () => payAnnualFee(student.barcode, feeType);
        });
    }

    // دالة دفع المصاريف السنوية
    window.payAnnualFee = function(barcode, feeType) {
        const student = students.find(s => s.barcode === barcode);
        if (!student || !student.annualFees) return;

        const amount = feeType === 'registration' ? student.annualFees.registration : student.annualFees.books;
        if (amount <= 0) {
            alert('لم يتم تحديد مبلغ لهذه المصاريف.');
            return;
        }

        const feeTypeName = feeType === 'registration' ? 'مصاريف التسجيل' : 'مصاريف الكتب المدرسية';
        const confirmPayment = confirm(`هل تريد دفع ${feeTypeName} بمبلغ ${amount.toLocaleString()} DHS؟`);

        if (confirmPayment) {
            if (feeType === 'registration') {
                student.annualFees.registrationPaid = true;
            } else {
                student.annualFees.booksPaid = true;
            }

            saveToStorage('sbea_students', students);
            displayAnnualFees(student);

            if (typeof renderStudents === 'function') {
                renderStudents(); // تحديث الجدول الرئيسي
            }

            alert(`تم دفع ${feeTypeName} بنجاح!`);
        }
    };

    // وظائف صفحة عمليات الدفع
    if (window.location.pathname.includes('payments.html')) {
        initializePaymentsPage();
    }

    // وظائف صفحة WhatsApp
    if (window.location.pathname.includes('whatsapp.html')) {
        initializeWhatsAppPage();
    }

    // وظائف صفحة الاستيراد والتصدير
    if (window.location.pathname.includes('import-export.html')) {
        initializeImportExportPage();
    }

    // دالة تهيئة صفحة عمليات الدفع
    function initializePaymentsPage() {
        // تهيئة التبويبات
        const reportTabs = document.querySelectorAll('.report-tab-btn');
        const reportContents = document.querySelectorAll('.report-tab-content');

        reportTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const targetTab = tab.dataset.tab;

                // إزالة الفئة النشطة من جميع التبويبات
                reportTabs.forEach(t => t.classList.remove('active'));
                reportContents.forEach(c => c.classList.remove('active'));

                // إضافة الفئة النشطة للتبويب المحدد
                tab.classList.add('active');
                document.getElementById(targetTab + '-tab').classList.add('active');

                // تحديث المحتوى حسب التبويب
                updateTabContent(targetTab);
            });
        });

        // تحديث الإحصائيات
        updatePaymentsStats();

        // تحديث قائمة العمليات
        updateOperationsList();

        // معالجات الأحداث للفلاتر
        document.getElementById('apply-filters-btn').addEventListener('click', applyPaymentsFilters);
        document.getElementById('clear-filters-btn').addEventListener('click', clearPaymentsFilters);
        document.getElementById('export-payments-btn').addEventListener('click', exportPaymentsData);
        document.getElementById('refresh-operations-btn').addEventListener('click', updateOperationsList);

        // معالجات التقارير
        document.getElementById('generate-daily-btn').addEventListener('click', generateDailyReport);
        document.getElementById('generate-monthly-btn').addEventListener('click', generateMonthlyReport);
        document.getElementById('generate-annual-btn').addEventListener('click', generateAnnualReport);

        // تعيين التاريخ الحالي
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('daily-date').value = today;
        document.getElementById('monthly-month').value = new Date().getMonth() + 1;
        document.getElementById('monthly-year').value = new Date().getFullYear();
        document.getElementById('annual-year').value = new Date().getFullYear();
    }

    // دالة تحديث إحصائيات الدفعات
    function updatePaymentsStats() {
        const operations = getPaymentOperations();
        const today = new Date().toDateString();
        const currentMonth = new Date().getMonth();
        const currentYear = new Date().getFullYear();

        const totalOperations = operations.length;
        const totalAmount = operations.reduce((sum, op) => sum + parseFloat(op.amount || 0), 0);
        const todayOperations = operations.filter(op => new Date(op.date).toDateString() === today).length;
        const monthOperations = operations.filter(op => {
            const opDate = new Date(op.date);
            return opDate.getMonth() === currentMonth && opDate.getFullYear() === currentYear;
        }).length;

        document.getElementById('total-operations').textContent = totalOperations.toLocaleString();
        document.getElementById('total-amount').textContent = totalAmount.toLocaleString() + ' DHS';
        document.getElementById('today-operations').textContent = todayOperations.toLocaleString();
        document.getElementById('month-operations').textContent = monthOperations.toLocaleString();
    }

    // دالة الحصول على عمليات الدفع
    function getPaymentOperations() {
        const operations = [];
        const students = loadFromStorage('sbea_students') || [];

        students.forEach(student => {
            // الدفعات الشهرية
            if (student.monthlyPayments) {
                Object.entries(student.monthlyPayments).forEach(([month, payment]) => {
                    if (payment.paid) {
                        operations.push({
                            id: `monthly_${student.barcode}_${month}`,
                            studentName: student.name,
                            studentBarcode: student.barcode,
                            type: 'monthly',
                            description: `دفعة شهرية - ${month}`,
                            amount: payment.amount,
                            date: payment.paidDate || new Date().toISOString(),
                            month: month
                        });
                    }
                });
            }

            // المصاريف السنوية
            if (student.annualFees) {
                if (student.annualFees.registrationPaid) {
                    operations.push({
                        id: `registration_${student.barcode}`,
                        studentName: student.name,
                        studentBarcode: student.barcode,
                        type: 'registration',
                        description: 'مصاريف التسجيل',
                        amount: student.annualFees.registration,
                        date: new Date().toISOString()
                    });
                }

                if (student.annualFees.booksPaid) {
                    operations.push({
                        id: `books_${student.barcode}`,
                        studentName: student.name,
                        studentBarcode: student.barcode,
                        type: 'books',
                        description: 'مصاريف الكتب المدرسية',
                        amount: student.annualFees.books,
                        date: new Date().toISOString()
                    });
                }
            }

            // الواجبات الإضافية
            if (student.additionalFees) {
                student.additionalFees.forEach((fee, index) => {
                    if (fee.paid) {
                        operations.push({
                            id: `additional_${student.barcode}_${index}`,
                            studentName: student.name,
                            studentBarcode: student.barcode,
                            type: 'additional',
                            description: fee.description,
                            amount: fee.amount,
                            date: fee.paidDate || new Date().toISOString()
                        });
                    }
                });
            }
        });

        return operations.sort((a, b) => new Date(b.date) - new Date(a.date));
    }

    // دالة تحديث قائمة العمليات
    function updateOperationsList() {
        const operations = getPaymentOperations();
        const operationsList = document.getElementById('operations-list');

        if (!operationsList) return;

        let html = `
            <table>
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>اسم التلميذ</th>
                        <th>الرقم التسلسلي</th>
                        <th>نوع الدفع</th>
                        <th>الوصف</th>
                        <th>المبلغ</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
        `;

        operations.forEach(operation => {
            const date = new Date(operation.date).toLocaleDateString('ar-MA');
            const typeNames = {
                'monthly': 'دفعة شهرية',
                'transport': 'رسوم النقل',
                'registration': 'مصاريف التسجيل',
                'books': 'مصاريف الكتب',
                'additional': 'واجب إضافي'
            };

            html += `
                <tr>
                    <td>${date}</td>
                    <td>${operation.studentName}</td>
                    <td>${operation.studentBarcode}</td>
                    <td>${typeNames[operation.type] || operation.type}</td>
                    <td>${operation.description}</td>
                    <td>${parseFloat(operation.amount).toLocaleString()} DHS</td>
                    <td>
                        <button onclick="viewOperationDetails('${operation.id}')" class="view-btn">
                            <i class="fas fa-eye"></i> عرض
                        </button>
                    </td>
                </tr>
            `;
        });

        html += `
                </tbody>
            </table>
        `;

        operationsList.innerHTML = html;
    }

    // دالة عرض تفاصيل العملية
    window.viewOperationDetails = function(operationId) {
        const operations = getPaymentOperations();
        const operation = operations.find(op => op.id === operationId);

        if (!operation) return;

        const modal = document.getElementById('operation-details-modal');
        const content = document.getElementById('operation-details-content');

        const date = new Date(operation.date).toLocaleDateString('ar-MA');
        const time = new Date(operation.date).toLocaleTimeString('ar-MA');

        content.innerHTML = `
            <div class="operation-details">
                <div class="detail-row">
                    <strong>رقم العملية:</strong>
                    <span>${operation.id}</span>
                </div>
                <div class="detail-row">
                    <strong>التاريخ:</strong>
                    <span>${date}</span>
                </div>
                <div class="detail-row">
                    <strong>الوقت:</strong>
                    <span>${time}</span>
                </div>
                <div class="detail-row">
                    <strong>اسم التلميذ:</strong>
                    <span>${operation.studentName}</span>
                </div>
                <div class="detail-row">
                    <strong>الرقم التسلسلي:</strong>
                    <span>${operation.studentBarcode}</span>
                </div>
                <div class="detail-row">
                    <strong>نوع الدفع:</strong>
                    <span>${operation.type}</span>
                </div>
                <div class="detail-row">
                    <strong>الوصف:</strong>
                    <span>${operation.description}</span>
                </div>
                <div class="detail-row">
                    <strong>المبلغ:</strong>
                    <span>${parseFloat(operation.amount).toLocaleString()} DHS</span>
                </div>
                ${operation.month ? `
                <div class="detail-row">
                    <strong>الشهر:</strong>
                    <span>${operation.month}</span>
                </div>
                ` : ''}
            </div>
        `;

        modal.style.display = 'block';
    };

    // دالة تهيئة صفحة WhatsApp
    function initializeWhatsAppPage() {
        // تهيئة التبويبات
        const whatsappTabs = document.querySelectorAll('.whatsapp-tab-btn');
        const whatsappContents = document.querySelectorAll('.whatsapp-tab-content');

        whatsappTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const targetTab = tab.dataset.tab;

                whatsappTabs.forEach(t => t.classList.remove('active'));
                whatsappContents.forEach(c => c.classList.remove('active'));

                tab.classList.add('active');
                document.getElementById(targetTab + '-tab').classList.add('active');

                updateWhatsAppTabContent(targetTab);
            });
        });

        // تحديث قائمة جهات الاتصال
        updateIndividualContacts();

        // تحديث مجموعات الأفواج
        updateWhatsAppGroups();

        // معالجات الأحداث
        document.getElementById('search-individual-btn').addEventListener('click', searchIndividualContacts);
        document.getElementById('create-group-btn').addEventListener('click', openCreateGroupModal);
        document.getElementById('send-broadcast-btn').addEventListener('click', sendBroadcastMessage);
        document.getElementById('create-template-btn').addEventListener('click', openCreateTemplateModal);

        // معالج تغيير نوع المستقبلين
        document.querySelectorAll('input[name="recipient-type"]').forEach(radio => {
            radio.addEventListener('change', updateRecipientSelection);
        });

        // معالج معاينة الرسالة
        document.getElementById('broadcast-message').addEventListener('input', updateMessagePreview);

        // تحديث قوالب الرسائل
        updateMessageTemplates();
    }

    // دالة تحديث جهات الاتصال الفردية
    function updateIndividualContacts() {
        const students = loadFromStorage('sbea_students') || [];
        const contactsContainer = document.getElementById('individual-contacts');

        if (!contactsContainer) return;

        let html = '';
        students.forEach(student => {
            if (student.phone) {
                const initials = student.name.split(' ').map(n => n[0]).join('').substring(0, 2);
                html += `
                    <div class="contact-card">
                        <div class="contact-info">
                            <div class="contact-avatar">${initials}</div>
                            <div class="contact-details">
                                <h5>${student.name}</h5>
                                <p>${student.level} - ${student.group}</p>
                                <p>${student.phone}</p>
                            </div>
                        </div>
                        <div class="contact-actions">
                            <button class="whatsapp-btn" onclick="openWhatsAppChat('${student.phone}', '${student.name}')">
                                <i class="fab fa-whatsapp"></i> محادثة
                            </button>
                        </div>
                    </div>
                `;
            }
        });

        contactsContainer.innerHTML = html || '<p>لا توجد جهات اتصال متاحة</p>';
    }

    // دالة فتح محادثة WhatsApp
    window.openWhatsAppChat = function(phone, name) {
        const message = `مرحباً، هذه رسالة من مؤسسة النور التربوي بخصوص التلميذ(ة) ${name}`;
        const encodedMessage = encodeURIComponent(message);
        const whatsappUrl = `https://wa.me/${phone.replace(/[^0-9]/g, '')}?text=${encodedMessage}`;
        window.open(whatsappUrl, '_blank');
    };

    // دالة تحديث مجموعات WhatsApp
    function updateWhatsAppGroups() {
        const groups = loadFromStorage('sbea_groups') || [];
        const groupsContainer = document.getElementById('whatsapp-groups');

        if (!groupsContainer) return;

        let html = '';
        groups.forEach(group => {
            const students = loadFromStorage('sbea_students') || [];
            const groupStudents = students.filter(s => s.level === group.level && s.group === group.name);
            const membersCount = groupStudents.length;

            html += `
                <div class="group-card">
                    <div class="group-info">
                        <div class="group-avatar">${group.name[0]}</div>
                        <div class="group-details">
                            <h5>${group.level} - ${group.name}</h5>
                            <p>${membersCount} عضو</p>
                        </div>
                    </div>
                    <div class="group-actions">
                        <button class="whatsapp-btn" onclick="createWhatsAppGroup('${group.level}', '${group.name}')">
                            <i class="fab fa-whatsapp"></i> إنشاء مجموعة
                        </button>
                        <button class="whatsapp-btn" onclick="sendGroupMessage('${group.level}', '${group.name}')">
                            <i class="fas fa-paper-plane"></i> رسالة جماعية
                        </button>
                    </div>
                </div>
            `;
        });

        groupsContainer.innerHTML = html || '<p>لا توجد مجموعات متاحة</p>';
    }

    // دالة إنشاء مجموعة WhatsApp
    window.createWhatsAppGroup = function(level, groupName) {
        const students = loadFromStorage('sbea_students') || [];
        const groupStudents = students.filter(s => s.level === level && s.group === groupName && s.phone);

        if (groupStudents.length === 0) {
            alert('لا توجد أرقام هواتف متاحة لهذا الفوج');
            return;
        }

        const phones = groupStudents.map(s => s.phone.replace(/[^0-9]/g, '')).join(',');
        const groupTitle = `${level} - ${groupName} - مؤسسة النور التربوي`;
        const message = `مرحباً بكم في مجموعة ${groupTitle}`;

        const whatsappUrl = `https://chat.whatsapp.com/invite?phones=${phones}&text=${encodeURIComponent(message)}`;
        window.open(whatsappUrl, '_blank');
    };

    // دالة إرسال رسالة جماعية للفوج
    window.sendGroupMessage = function(level, groupName) {
        const students = loadFromStorage('sbea_students') || [];
        const groupStudents = students.filter(s => s.level === level && s.group === groupName && s.phone);

        if (groupStudents.length === 0) {
            alert('لا توجد أرقام هواتف متاحة لهذا الفوج');
            return;
        }

        const message = prompt(`أدخل الرسالة التي تريد إرسالها لفوج ${level} - ${groupName}:`);
        if (!message) return;

        groupStudents.forEach(student => {
            const personalizedMessage = message.replace('{اسم_التلميذ}', student.name)
                                              .replace('{المستوى}', student.level)
                                              .replace('{الفوج}', student.group);

            const encodedMessage = encodeURIComponent(personalizedMessage);
            const whatsappUrl = `https://wa.me/${student.phone.replace(/[^0-9]/g, '')}?text=${encodedMessage}`;

            setTimeout(() => {
                window.open(whatsappUrl, '_blank');
            }, 1000); // تأخير ثانية واحدة بين كل رسالة
        });
    };

    // دالة تحديث اختيار المستقبلين
    function updateRecipientSelection() {
        const selectedType = document.querySelector('input[name="recipient-type"]:checked').value;

        // إخفاء جميع الخيارات
        document.querySelectorAll('.conditional-selection').forEach(el => {
            el.style.display = 'none';
        });

        // إظهار الخيار المحدد
        if (selectedType === 'level') {
            document.getElementById('level-selection').style.display = 'block';
            updateLevelOptions();
        } else if (selectedType === 'group') {
            document.getElementById('group-selection').style.display = 'block';
            updateGroupOptions();
        } else if (selectedType === 'custom') {
            document.getElementById('custom-selection').style.display = 'block';
            updateCustomContactsList();
        }
    }

    // دالة تحديث خيارات المستويات
    function updateLevelOptions() {
        const groups = loadFromStorage('sbea_groups') || [];
        const levels = [...new Set(groups.map(g => g.level))];
        const levelSelect = document.getElementById('broadcast-level');

        if (!levelSelect) return;

        levelSelect.innerHTML = '<option value="">اختر المستوى</option>';
        levels.forEach(level => {
            levelSelect.innerHTML += `<option value="${level}">${level}</option>`;
        });
    }

    // دالة تحديث خيارات الأفواج
    function updateGroupOptions() {
        const groups = loadFromStorage('sbea_groups') || [];
        const groupSelect = document.getElementById('broadcast-group');

        if (!groupSelect) return;

        groupSelect.innerHTML = '<option value="">اختر الفوج</option>';
        groups.forEach(group => {
            groupSelect.innerHTML += `<option value="${group.level}-${group.name}">${group.level} - ${group.name}</option>`;
        });
    }

    // دالة تحديث قائمة جهات الاتصال المخصصة
    function updateCustomContactsList() {
        const students = loadFromStorage('sbea_students') || [];
        const contactsList = document.getElementById('custom-contacts-list');

        if (!contactsList) return;

        let html = '';
        students.forEach(student => {
            if (student.phone) {
                html += `
                    <label>
                        <input type="checkbox" value="${student.barcode}">
                        ${student.name} - ${student.level} ${student.group} (${student.phone})
                    </label>
                `;
            }
        });

        contactsList.innerHTML = html || '<p>لا توجد جهات اتصال متاحة</p>';
    }

    // دالة تحديث معاينة الرسالة
    function updateMessagePreview() {
        const message = document.getElementById('broadcast-message').value;
        const preview = document.getElementById('message-preview-content');

        if (!preview) return;

        if (message.trim()) {
            // استبدال المتغيرات بقيم تجريبية
            const previewMessage = message
                .replace(/{اسم_التلميذ}/g, 'أحمد محمد')
                .replace(/{المستوى}/g, 'السادس ابتدائي')
                .replace(/{الفوج}/g, 'فوج أ')
                .replace(/{المبلغ}/g, '500')
                .replace(/{التاريخ}/g, new Date().toLocaleDateString('ar-MA'));

            preview.innerHTML = previewMessage;
        } else {
            preview.innerHTML = 'اكتب رسالتك لرؤية المعاينة...';
        }
    }

    // دالة إرسال الرسالة الجماعية
    function sendBroadcastMessage() {
        const recipientType = document.querySelector('input[name="recipient-type"]:checked').value;
        const message = document.getElementById('broadcast-message').value.trim();

        if (!message) {
            alert('يرجى كتابة الرسالة أولاً');
            return;
        }

        let recipients = [];
        const students = loadFromStorage('sbea_students') || [];

        // تحديد المستقبلين حسب النوع
        switch (recipientType) {
            case 'all':
                recipients = students.filter(s => s.phone);
                break;
            case 'level':
                const selectedLevel = document.getElementById('broadcast-level').value;
                if (!selectedLevel) {
                    alert('يرجى اختيار المستوى');
                    return;
                }
                recipients = students.filter(s => s.level === selectedLevel && s.phone);
                break;
            case 'group':
                const selectedGroup = document.getElementById('broadcast-group').value;
                if (!selectedGroup) {
                    alert('يرجى اختيار الفوج');
                    return;
                }
                const [level, group] = selectedGroup.split('-');
                recipients = students.filter(s => s.level === level && s.group === group && s.phone);
                break;
            case 'custom':
                const checkedBoxes = document.querySelectorAll('#custom-contacts-list input[type="checkbox"]:checked');
                const selectedBarcodes = Array.from(checkedBoxes).map(cb => cb.value);
                recipients = students.filter(s => selectedBarcodes.includes(s.barcode) && s.phone);
                break;
        }

        if (recipients.length === 0) {
            alert('لا توجد جهات اتصال متاحة للإرسال');
            return;
        }

        const confirmSend = confirm(`هل تريد إرسال الرسالة إلى ${recipients.length} شخص؟`);
        if (!confirmSend) return;

        // إرسال الرسائل
        recipients.forEach((student, index) => {
            const personalizedMessage = message
                .replace(/{اسم_التلميذ}/g, student.name)
                .replace(/{المستوى}/g, student.level)
                .replace(/{الفوج}/g, student.group)
                .replace(/{المبلغ}/g, student.fee || '0')
                .replace(/{التاريخ}/g, new Date().toLocaleDateString('ar-MA'));

            const encodedMessage = encodeURIComponent(personalizedMessage);
            const whatsappUrl = `https://wa.me/${student.phone.replace(/[^0-9]/g, '')}?text=${encodedMessage}`;

            setTimeout(() => {
                window.open(whatsappUrl, '_blank');
            }, index * 2000); // تأخير ثانيتين بين كل رسالة
        });

        alert(`تم فتح ${recipients.length} نافذة WhatsApp للإرسال`);
    }

    // دالة تحديث قوالب الرسائل
    function updateMessageTemplates() {
        const templates = loadFromStorage('sbea_message_templates') || getDefaultTemplates();
        const templatesList = document.getElementById('templates-list');

        if (!templatesList) return;

        let html = '';
        templates.forEach((template, index) => {
            html += `
                <div class="template-card">
                    <div class="template-header">
                        <h5>${template.name}</h5>
                        <span class="template-category-badge">${getCategoryName(template.category)}</span>
                    </div>
                    <div class="template-content">${template.content}</div>
                    <div class="template-actions">
                        <button class="use-template-btn" onclick="useTemplate(${index})">استخدام</button>
                        <button class="edit-template-btn" onclick="editTemplate(${index})">تعديل</button>
                        <button class="delete-template-btn" onclick="deleteTemplate(${index})">حذف</button>
                    </div>
                </div>
            `;
        });

        templatesList.innerHTML = html || '<p>لا توجد قوالب متاحة</p>';
    }

    // دالة الحصول على القوالب الافتراضية
    function getDefaultTemplates() {
        return [
            {
                name: 'تذكير بالدفع الشهري',
                category: 'payment',
                content: 'مرحباً، نذكركم بضرورة دفع المستحقات الشهرية للتلميذ(ة) {اسم_التلميذ} من {المستوى} - {الفوج} بمبلغ {المبلغ} DHS. شكراً لتعاونكم.'
            },
            {
                name: 'إشعار اجتماع أولياء الأمور',
                category: 'academic',
                content: 'مرحباً، ندعوكم لحضور اجتماع أولياء الأمور يوم {التاريخ} في تمام الساعة 6 مساءً بقاعة الاجتماعات. الموضوع: مناقشة نتائج الفصل الدراسي.'
            },
            {
                name: 'تهنئة بالنجاح',
                category: 'academic',
                content: 'مبروك! نهنئكم بنجاح التلميذ(ة) {اسم_التلميذ} وانتقاله للمستوى التالي. نتمنى له المزيد من التفوق والنجاح.'
            },
            {
                name: 'دعوة لحفل التخرج',
                category: 'events',
                content: 'يسعدنا دعوتكم لحضور حفل تخرج التلميذ(ة) {اسم_التلميذ} يوم {التاريخ}. نتطلع لمشاركتكم هذه اللحظات المميزة.'
            }
        ];
    }

    // دالة الحصول على اسم الفئة
    function getCategoryName(category) {
        const names = {
            'payment': 'تذكير بالدفع',
            'academic': 'أكاديمية',
            'events': 'فعاليات',
            'general': 'عامة'
        };
        return names[category] || category;
    }

    // دالة استخدام القالب
    window.useTemplate = function(index) {
        const templates = loadFromStorage('sbea_message_templates') || getDefaultTemplates();
        const template = templates[index];

        if (template) {
            document.getElementById('broadcast-message').value = template.content;
            updateMessagePreview();

            // التبديل إلى تبويب الرسائل الجماعية
            document.querySelector('[data-tab="broadcast"]').click();
        }
    };

    // دالة تعديل القالب
    window.editTemplate = function(index) {
        const templates = loadFromStorage('sbea_message_templates') || getDefaultTemplates();
        const template = templates[index];

        if (template) {
            document.getElementById('template-name').value = template.name;
            document.getElementById('template-category').value = template.category;
            document.getElementById('template-content').value = template.content;

            // فتح نافذة التعديل
            document.getElementById('create-template-modal').style.display = 'block';

            // حفظ الفهرس للتعديل
            document.getElementById('create-template-form').dataset.editIndex = index;
        }
    };

    // دالة حذف القالب
    window.deleteTemplate = function(index) {
        const confirmDelete = confirm('هل تريد حذف هذا القالب؟');
        if (!confirmDelete) return;

        const templates = loadFromStorage('sbea_message_templates') || getDefaultTemplates();
        templates.splice(index, 1);
        saveToStorage('sbea_message_templates', templates);
        updateMessageTemplates();
    };

    // دالة فتح نافذة إنشاء القالب
    function openCreateTemplateModal() {
        document.getElementById('create-template-modal').style.display = 'block';
        document.getElementById('create-template-form').reset();
        delete document.getElementById('create-template-form').dataset.editIndex;
    }

    // دالة تهيئة صفحة الاستيراد والتصدير
    function initializeImportExportPage() {
        // تهيئة التبويبات
        const ieTabs = document.querySelectorAll('.ie-tab-btn');
        const ieContents = document.querySelectorAll('.ie-tab-content');

        ieTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const targetTab = tab.dataset.tab;

                ieTabs.forEach(t => t.classList.remove('active'));
                ieContents.forEach(c => c.classList.remove('active'));

                tab.classList.add('active');
                document.getElementById(targetTab + '-tab').classList.add('active');
            });
        });

        // معالجات تحميل النماذج
        document.querySelectorAll('.download-template-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const templateType = e.target.dataset.type;
                downloadTemplate(templateType);
            });
        });

        // معالجات الاستيراد
        const uploadZone = document.getElementById('upload-zone');
        const fileInput = document.getElementById('import-file');

        if (uploadZone && fileInput) {
            uploadZone.addEventListener('click', () => fileInput.click());
            uploadZone.addEventListener('dragover', handleDragOver);
            uploadZone.addEventListener('drop', handleFileDrop);
            fileInput.addEventListener('change', handleFileSelect);
        }

        // معالجات التصدير
        document.querySelectorAll('.export-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const exportType = e.target.dataset.type;
                exportData(exportType);
            });
        });

        // معالجات النسخ الاحتياطي
        document.getElementById('create-backup-btn').addEventListener('click', createBackup);
        document.getElementById('select-restore-file-btn').addEventListener('click', () => {
            document.getElementById('restore-file').click();
        });
        document.getElementById('restore-file').addEventListener('change', handleRestoreFileSelect);
        document.getElementById('restore-backup-btn').addEventListener('click', restoreBackup);

        // تحديث قائمة النسخ الاحتياطية
        updateBackupList();
    }

    // دالة تحميل النماذج
    function downloadTemplate(type) {
        let templateData = [];
        let filename = '';

        switch (type) {
            case 'students':
                templateData = [
                    {
                        'الاسم الكامل': 'أحمد محمد علي',
                        'المستوى': 'السادس ابتدائي',
                        'الفوج': 'فوج أ',
                        'المستحقات الشهرية': '500',
                        'رقم هاتف ولي الأمر': '0612345678',
                        'يستفيد من النقل': 'نعم',
                        'رسوم النقل الشهرية': '200',
                        'مصاريف التسجيل': '300',
                        'مصاريف الكتب المدرسية': '250'
                    }
                ];
                filename = 'نموذج_التلاميذ.xlsx';
                break;

            case 'teachers':
                templateData = [
                    {
                        'الاسم الكامل': 'فاطمة الزهراء',
                        'رقم الهاتف': '0612345678',
                        'الراتب الشهري': '8000',
                        'المواد المدرسة': 'الرياضيات، الفيزياء',
                        'المستويات': 'السادس ابتدائي، الأولى إعدادي',
                        'الأفواج': 'فوج أ، فوج ب'
                    }
                ];
                filename = 'نموذج_الأساتذة.xlsx';
                break;

            case 'staff':
                templateData = [
                    {
                        'الاسم الكامل': 'محمد الإداري',
                        'رقم الهاتف': '0612345678',
                        'نوع الوظيفة': 'إداري',
                        'الراتب الشهري': '4000',
                        'الوردية': '',
                        'المنطقة المسؤول عنها': ''
                    }
                ];
                filename = 'نموذج_الموظفين.xlsx';
                break;

            case 'groups':
                templateData = [
                    {
                        'المستوى': 'السادس ابتدائي',
                        'اسم الفوج': 'فوج أ',
                        'المقررات الدراسية': 'الرياضيات، اللغة العربية، الفرنسية',
                        'الأنشطة التعليمية': 'النشاط العلمي، التربية الفنية'
                    }
                ];
                filename = 'نموذج_المستويات_والأفواج.xlsx';
                break;
        }

        // إنشاء ملف Excel
        const ws = XLSX.utils.json_to_sheet(templateData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'البيانات');

        // تحميل الملف
        XLSX.writeFile(wb, filename);
    }

    // دالة معالجة السحب والإفلات
    function handleDragOver(e) {
        e.preventDefault();
        e.currentTarget.classList.add('dragover');
    }

    function handleFileDrop(e) {
        e.preventDefault();
        e.currentTarget.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect({ target: { files: files } });
        }
    }

    // دالة معالجة اختيار الملف
    function handleFileSelect(e) {
        const file = e.target.files[0];
        if (!file) return;

        const allowedTypes = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel'
        ];

        if (!allowedTypes.includes(file.type)) {
            alert('يرجى اختيار ملف Excel صحيح (.xlsx أو .xls)');
            return;
        }

        // تفعيل أزرار المعاينة والاستيراد
        document.getElementById('preview-import-btn').disabled = false;
        document.getElementById('start-import-btn').disabled = false;

        // حفظ الملف للمعالجة
        window.selectedImportFile = file;

        // تحديث نص منطقة الرفع
        const uploadZone = document.getElementById('upload-zone');
        uploadZone.innerHTML = `
            <div class="upload-icon">
                <i class="fas fa-file-excel"></i>
            </div>
            <div class="upload-text">
                <h4>تم اختيار الملف: ${file.name}</h4>
                <p>حجم الملف: ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                <small>انقر لاختيار ملف آخر</small>
            </div>
        `;
    }

    // دالة تصدير البيانات
    function exportData(type) {
        let data = [];
        let filename = '';

        switch (type) {
            case 'students':
                const students = loadFromStorage('sbea_students') || [];
                data = students.map(student => ({
                    'الرقم التسلسلي': student.barcode || '',
                    'الاسم الكامل': student.name || '',
                    'المستوى': student.level || '',
                    'الفوج': student.group || '',
                    'المستحقات الشهرية': student.fee || '',
                    'رقم هاتف ولي الأمر': student.phone || '',
                    'يستفيد من النقل': student.hasTransport ? 'نعم' : 'لا',
                    'رسوم النقل': student.transportFee || '0',
                    'مصاريف التسجيل': student.annualFees?.registration || '0',
                    'مصاريف الكتب': student.annualFees?.books || '0',
                    'حالة دفع التسجيل': student.annualFees?.registrationPaid ? 'مدفوع' : 'غير مدفوع',
                    'حالة دفع الكتب': student.annualFees?.booksPaid ? 'مدفوع' : 'غير مدفوع'
                }));
                filename = `بيانات_التلاميذ_${new Date().toISOString().split('T')[0]}.xlsx`;
                break;

            case 'teachers':
                const teachers = loadFromStorage('sbea_teachers') || [];
                data = teachers.map(teacher => ({
                    'الاسم الكامل': teacher.name || '',
                    'رقم الهاتف': teacher.phone || '',
                    'الراتب الشهري': teacher.salary || '',
                    'المواد المدرسة': teacher.subjects?.join(', ') || '',
                    'المستويات والأفواج': teacher.groups?.join(', ') || ''
                }));
                filename = `بيانات_الأساتذة_${new Date().toISOString().split('T')[0]}.xlsx`;
                break;

            case 'payments':
                const operations = getPaymentOperations();
                data = operations.map(op => ({
                    'التاريخ': new Date(op.date).toLocaleDateString('ar-MA'),
                    'اسم التلميذ': op.studentName,
                    'الرقم التسلسلي': op.studentBarcode,
                    'نوع الدفع': op.type,
                    'الوصف': op.description,
                    'المبلغ': op.amount,
                    'الشهر': op.month || ''
                }));
                filename = `عمليات_الدفع_${new Date().toISOString().split('T')[0]}.xlsx`;
                break;

            case 'reports':
                // تصدير تقرير شامل
                const studentsData = loadFromStorage('sbea_students') || [];
                const totalStudents = studentsData.length;
                const totalPaid = studentsData.reduce((sum, s) => {
                    let paid = 0;
                    if (s.monthlyPayments) {
                        Object.values(s.monthlyPayments).forEach(payment => {
                            if (payment.paid) paid += parseFloat(payment.amount || 0);
                        });
                    }
                    return sum + paid;
                }, 0);

                data = [
                    {
                        'البيان': 'إجمالي عدد التلاميذ',
                        'القيمة': totalStudents
                    },
                    {
                        'البيان': 'إجمالي المبالغ المحصلة',
                        'القيمة': totalPaid.toLocaleString() + ' DHS'
                    },
                    {
                        'البيان': 'تاريخ التقرير',
                        'القيمة': new Date().toLocaleDateString('ar-MA')
                    }
                ];
                filename = `تقرير_شامل_${new Date().toISOString().split('T')[0]}.xlsx`;
                break;
        }

        if (data.length === 0) {
            alert('لا توجد بيانات للتصدير');
            return;
        }

        // إنشاء ملف Excel
        const ws = XLSX.utils.json_to_sheet(data);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'البيانات');

        // تحميل الملف
        XLSX.writeFile(wb, filename);

        alert(`تم تصدير ${data.length} سجل بنجاح`);
    }

    // دالة إنشاء نسخة احتياطية
    function createBackup() {
        const backupData = {
            students: loadFromStorage('sbea_students') || [],
            teachers: loadFromStorage('sbea_teachers') || [],
            staff: loadFromStorage('sbea_staff') || [],
            groups: loadFromStorage('sbea_groups') || [],
            settings: loadFromStorage('sbea_settings') || {},
            messageTemplates: loadFromStorage('sbea_message_templates') || [],
            timestamp: new Date().toISOString(),
            version: '1.0'
        };

        const dataStr = JSON.stringify(backupData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `نسخة_احتياطية_${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        // حفظ معلومات النسخة الاحتياطية
        const backups = loadFromStorage('sbea_backups') || [];
        backups.unshift({
            id: Date.now(),
            date: new Date().toISOString(),
            size: dataStr.length,
            records: {
                students: backupData.students.length,
                teachers: backupData.teachers.length,
                staff: backupData.staff.length,
                groups: backupData.groups.length
            }
        });

        // الاحتفاظ بآخر 10 نسخ احتياطية فقط
        if (backups.length > 10) {
            backups.splice(10);
        }

        saveToStorage('sbea_backups', backups);
        updateBackupList();

        alert('تم إنشاء النسخة الاحتياطية بنجاح');
    }

    // دالة معالجة اختيار ملف الاستعادة
    function handleRestoreFileSelect(e) {
        const file = e.target.files[0];
        if (file) {
            document.getElementById('restore-backup-btn').disabled = false;
            window.selectedRestoreFile = file;
        }
    }

    // دالة استعادة النسخة الاحتياطية
    function restoreBackup() {
        if (!window.selectedRestoreFile) {
            alert('يرجى اختيار ملف النسخة الاحتياطية أولاً');
            return;
        }

        const confirmRestore = confirm('تحذير: ستؤدي هذه العملية إلى استبدال جميع البيانات الحالية. هل تريد المتابعة؟');
        if (!confirmRestore) return;

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const backupData = JSON.parse(e.target.result);

                // التحقق من صحة البيانات
                if (!backupData.timestamp || !backupData.version) {
                    throw new Error('ملف النسخة الاحتياطية غير صحيح');
                }

                // استعادة البيانات
                if (backupData.students) saveToStorage('sbea_students', backupData.students);
                if (backupData.teachers) saveToStorage('sbea_teachers', backupData.teachers);
                if (backupData.staff) saveToStorage('sbea_staff', backupData.staff);
                if (backupData.groups) saveToStorage('sbea_groups', backupData.groups);
                if (backupData.settings) saveToStorage('sbea_settings', backupData.settings);
                if (backupData.messageTemplates) saveToStorage('sbea_message_templates', backupData.messageTemplates);

                alert('تم استعادة النسخة الاحتياطية بنجاح. سيتم إعادة تحميل الصفحة.');
                window.location.reload();

            } catch (error) {
                alert('خطأ في قراءة ملف النسخة الاحتياطية: ' + error.message);
            }
        };

        reader.readAsText(window.selectedRestoreFile);
    }

    // دالة تحديث قائمة النسخ الاحتياطية
    function updateBackupList() {
        const backups = loadFromStorage('sbea_backups') || [];
        const backupList = document.getElementById('backup-list');

        if (!backupList) return;

        if (backups.length === 0) {
            backupList.innerHTML = '<p>لا توجد نسخ احتياطية</p>';
            return;
        }

        let html = '';
        backups.forEach(backup => {
            const date = new Date(backup.date).toLocaleDateString('ar-MA');
            const time = new Date(backup.date).toLocaleTimeString('ar-MA');
            const size = (backup.size / 1024).toFixed(2);

            html += `
                <div class="backup-item">
                    <div class="backup-info">
                        <h6>نسخة احتياطية - ${date}</h6>
                        <p>${time} | ${size} KB | ${backup.records.students} تلميذ | ${backup.records.teachers} أستاذ</p>
                    </div>
                    <div class="backup-actions-item">
                        <button class="download-backup-btn" onclick="downloadBackup(${backup.id})">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="delete-backup-btn" onclick="deleteBackup(${backup.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        });

        backupList.innerHTML = html;
    }

    // دالة حذف نسخة احتياطية
    window.deleteBackup = function(backupId) {
        const confirmDelete = confirm('هل تريد حذف هذه النسخة الاحتياطية؟');
        if (!confirmDelete) return;

        const backups = loadFromStorage('sbea_backups') || [];
        const updatedBackups = backups.filter(backup => backup.id !== backupId);
        saveToStorage('sbea_backups', updatedBackups);
        updateBackupList();
    };

    // ===================================================================
    //                    دوال مركزية لربط الصفحات
    // ===================================================================

    // دالة تحديث جميع قوائم المستويات في الصفحة
    function updateAllLevelSelects() {
        const levelSelects = document.querySelectorAll('select[id*="level"], select[id*="Level"]');
        levelSelects.forEach(select => {
            if (select && select.id !== 'student-level' && select.id !== 'edit-student-level') {
                const currentValue = select.value;
                select.innerHTML = '<option value="">اختر المستوى</option>';
                ACADEMIC_LEVELS.forEach(level => {
                    const option = document.createElement('option');
                    option.value = level;
                    option.textContent = level;
                    if (level === currentValue) option.selected = true;
                    select.appendChild(option);
                });
            }
        });
    }

    // دالة تحديث جميع قوائم الأفواج في الصفحة
    function updateAllGroupSelects() {
        const groupSelects = document.querySelectorAll('select[id*="group"], select[id*="Group"]');
        groupSelects.forEach(select => {
            if (select && select.id !== 'student-group' && select.id !== 'edit-student-group') {
                const currentValue = select.value;
                select.innerHTML = '<option value="">اختر الفوج</option>';
                AVAILABLE_GROUPS.forEach(group => {
                    const option = document.createElement('option');
                    option.value = group;
                    option.textContent = group;
                    if (group === currentValue) option.selected = true;
                    select.appendChild(option);
                });
            }
        });
    }

    // دالة تحديث جميع قوائم المواد في الصفحة
    function updateAllSubjectSelects() {
        const subjectSelects = document.querySelectorAll('select[id*="subject"], select[id*="Subject"]');
        subjectSelects.forEach(select => {
            if (select) {
                const currentValue = select.value;
                select.innerHTML = '<option value="">اختر المادة</option>';
                ALL_SUBJECTS.forEach(subject => {
                    const option = document.createElement('option');
                    option.value = subject;
                    option.textContent = subject;
                    if (subject === currentValue) option.selected = true;
                    select.appendChild(option);
                });
            }
        });
    }

    // دالة تحديث قوائم التلاميذ والأساتذة في الصفحات المختلفة
    function updateStudentAndTeacherSelects() {
        // تحديث قوائم التلاميذ
        const studentSelects = document.querySelectorAll('select[id*="student"]');
        studentSelects.forEach(select => {
            if (select) {
                const currentValue = select.value;
                select.innerHTML = '<option value="">اختر التلميذ</option>';
                students.forEach(student => {
                    const option = document.createElement('option');
                    option.value = student.barcode;
                    option.textContent = `${student.name} - ${student.level} ${student.group}`;
                    if (student.barcode === currentValue) option.selected = true;
                    select.appendChild(option);
                });
            }
        });

        // تحديث قوائم الأساتذة
        const teacherSelects = document.querySelectorAll('select[id*="teacher"]');
        teacherSelects.forEach(select => {
            if (select) {
                const currentValue = select.value;
                select.innerHTML = '<option value="">اختر الأستاذ</option>';
                teachers.forEach(teacher => {
                    const option = document.createElement('option');
                    option.value = teacher.id;
                    option.textContent = teacher.name;
                    if (teacher.id === currentValue) option.selected = true;
                    select.appendChild(option);
                });
            }
        });
    }

    // دالة تحديث جميع الاختيارات في الصفحة الحالية
    function updateAllSelects() {
        updateAllLevelSelects();
        updateAllGroupSelects();
        updateAllSubjectSelects();
        updateStudentAndTeacherSelects();
    }

    // دالة الحصول على المجموعات المتاحة لمستوى معين
    function getGroupsForLevel(level) {
        const levelGroups = groups.filter(g => g.level === level);
        return levelGroups.length > 0 ? levelGroups.map(g => g.name) : AVAILABLE_GROUPS;
    }

    // دالة الحصول على التلاميذ لمستوى وفوج معين
    function getStudentsForLevelAndGroup(level, group) {
        return students.filter(s => s.level === level && s.group === group);
    }

    // دالة الحصول على الأساتذة لمادة معينة
    function getTeachersForSubject(subject) {
        return teachers.filter(t => t.subjects && t.subjects.includes(subject));
    }

    // دالة مزامنة البيانات بين الصفحات
    function syncDataAcrossPages() {
        // إعادة تحميل البيانات من التخزين المحلي
        students = loadFromStorage('sbea_students') || [];
        teachers = loadFromStorage('sbea_teachers') || [];
        groups = loadFromStorage('sbea_groups') || [];

        // تحديث جميع الاختيارات
        updateAllSelects();

        // تحديث العروض إذا كانت الدوال متاحة
        if (typeof renderStudents === 'function') {
            renderStudents();
        }
        // تحديث الصفحة الجديدة للطلاب
        if (typeof renderStudentsTable === 'function') {
            renderStudentsTable();
        }
        if (typeof updateStatistics === 'function') {
            updateStatistics();
        }
        if (typeof displayTeachers === 'function') {
            displayTeachers();
        }
        if (typeof displayGroups === 'function') {
            displayGroups();
        }
        if (typeof updateDashboardStats === 'function') {
            updateDashboardStats();
        }
    }

    // دالة تهيئة الصفحة الحالية
    function initializeCurrentPage() {
        const currentPage = window.location.pathname.split('/').pop();

        // تحديث جميع الاختيارات
        updateAllSelects();

        // إضافة مستمعي الأحداث للمزامنة
        window.addEventListener('storage', syncDataAcrossPages);

        // تحديث دوري للبيانات (كل 30 ثانية)
        setInterval(syncDataAcrossPages, 30000);

        // تهيئة خاصة حسب الصفحة
        switch (currentPage) {
            case 'students.html':
                initializeStudentsPage();
                break;
            case 'teachers.html':
                initializeTeachersPage();
                break;
            case 'groups.html':
                initializeGroupsPage();
                break;
            case 'whatsapp.html':
                initializeWhatsAppPage();
                break;
            case 'payments.html':
                initializePaymentsPage();
                break;
            case 'import-export.html':
                initializeImportExportPage();
                break;
        }
    }

    // دالة تهيئة صفحة التلاميذ
    function initializeStudentsPage() {
        // تحديث قوائم المستويات والأفواج
        const levelSelect = document.getElementById('student-level');
        const groupSelect = document.getElementById('student-group');
        const editLevelSelect = document.getElementById('edit-student-level');
        const editGroupSelect = document.getElementById('edit-student-group');

        if (levelSelect) {
            levelSelect.innerHTML = '<option value="">اختر المستوى الدراسي</option>';
            ACADEMIC_LEVELS.forEach(level => {
                const option = document.createElement('option');
                option.value = level;
                option.textContent = level;
                levelSelect.appendChild(option);
            });

            // إضافة مستمع لتحديث الأفواج عند تغيير المستوى
            levelSelect.addEventListener('change', () => {
                updateGroupSelectForLevel(groupSelect, levelSelect.value);
            });
        }

        if (editLevelSelect) {
            editLevelSelect.innerHTML = '<option value="">اختر المستوى الدراسي</option>';
            ACADEMIC_LEVELS.forEach(level => {
                const option = document.createElement('option');
                option.value = level;
                option.textContent = level;
                editLevelSelect.appendChild(option);
            });

            editLevelSelect.addEventListener('change', () => {
                updateGroupSelectForLevel(editGroupSelect, editLevelSelect.value);
            });
        }

        // تحديث الأفواج المتاحة
        updateGroupSelectForLevel(groupSelect, '');
        updateGroupSelectForLevel(editGroupSelect, '');
    }

    // دالة تحديث قائمة الأفواج حسب المستوى
    function updateGroupSelectForLevel(groupSelect, selectedLevel) {
        if (!groupSelect) return;

        const currentValue = groupSelect.value;
        groupSelect.innerHTML = '<option value="">اختر الفوج</option>';

        const availableGroups = selectedLevel ? getGroupsForLevel(selectedLevel) : AVAILABLE_GROUPS;
        availableGroups.forEach(group => {
            const option = document.createElement('option');
            option.value = group;
            option.textContent = group;
            if (group === currentValue) option.selected = true;
            groupSelect.appendChild(option);
        });
    }

    // دالة تهيئة صفحة الأساتذة
    function initializeTeachersPage() {
        updateAllSelects();
    }

    // دالة تهيئة صفحة المجموعات
    function initializeGroupsPage() {
        const levelSelect = document.getElementById('level-select');
        if (levelSelect) {
            levelSelect.innerHTML = '<option value="">اختر المستوى الدراسي</option>';
            ACADEMIC_LEVELS.forEach(level => {
                const option = document.createElement('option');
                option.value = level;
                option.textContent = level;
                levelSelect.appendChild(option);
            });
        }

        updateAllSelects();
    }

    // تهيئة الصفحة عند التحميل
    document.addEventListener('DOMContentLoaded', () => {
        // التحقق من تسجيل الدخول أولاً
        if (window.location.pathname.includes('login.html')) {
            return; // لا نحتاج تهيئة إضافية في صفحة تسجيل الدخول
        }

        // التحقق من المصادقة
        if (window.authSystem && !window.authSystem.isLoggedIn()) {
            window.location.href = 'login.html';
            return;
        }

        initializeCurrentPage();

        // تحديث فوري للاختيارات
        setTimeout(() => {
            updateAllSelects();

            // تحديث واجهة المستخدم
            if (window.authSystem) {
                window.authSystem.updateUserInterface();
            }

            // إعداد التحقق الفوري للنماذج
            setupFormValidation();

            // تطبيق الرسوم المتحركة
            if (window.animationSystem) {
                window.animationSystem.autoAnimate();
            }
        }, 100);

        // دالة إعداد التحقق الفوري للنماذج
        function setupFormValidation() {
            // نموذج التلاميذ
            const studentForm = document.getElementById('student-form');
            if (studentForm && window.validator) {
                window.validator.setupRealTimeValidation(studentForm, {
                    'student-name': ['required', 'arabicName', { name: 'minLength', params: [2] }],
                    'student-level': ['required'],
                    'student-group': ['required'],
                    'student-fee': ['required', 'positiveNumber'],
                    'student-phone': ['phone']
                });
            }

            // نموذج الأساتذة
            const teacherForm = document.getElementById('teacher-form');
            if (teacherForm && window.validator) {
                window.validator.setupRealTimeValidation(teacherForm, window.validationRules.teacher);
            }

            // نموذج المستخدمين
            const userForm = document.getElementById('user-form');
            if (userForm && window.validator) {
                window.validator.setupRealTimeValidation(userForm, window.validationRules.user);
            }

            // نموذج المجموعات
            const groupForm = document.getElementById('group-form');
            if (groupForm && window.validator) {
                window.validator.setupRealTimeValidation(groupForm, window.validationRules.group);
            }
        }
    });

    // تحديث الاختيارات عند تغيير البيانات
    window.addEventListener('dataUpdated', () => {
        syncDataAcrossPages();
    });

    // تحسين دالة الحفظ للتكامل مع المزامنة والإشعارات
    const originalSaveToStorage = saveToStorage;
    window.saveToStorage = function(key, data, showNotification = false) {
        try {
            // عرض محمل سريع
            const loadingId = window.showLoading('جاري الحفظ...', 'يرجى الانتظار');

            setTimeout(() => {
                try {
                    // حفظ البيانات محلياً
                    originalSaveToStorage(key, data);

                    // إرسال التحديث للنوافذ الأخرى
                    if (window.syncSystem) {
                        window.syncSystem.broadcastUpdate(key, data);
                    }

                    // إطلاق حدث التحديث
                    window.dispatchEvent(new CustomEvent('dataUpdated', { detail: { key, data } }));

                    // تسجيل النشاط
                    if (window.authSystem) {
                        const actionMap = {
                            'sbea_students': 'تحديث بيانات التلاميذ',
                            'sbea_teachers': 'تحديث بيانات الأساتذة',
                            'sbea_staff': 'تحديث بيانات الموظفين',
                            'sbea_groups': 'تحديث المجموعات',
                            'sbea_payments': 'تحديث عمليات الدفع',
                            'sbea_users': 'تحديث المستخدمين'
                        };

                        const description = actionMap[key] || `تحديث ${key}`;
                        window.authSystem.logUserActivity('update', description);
                    }

                    // إخفاء المحمل
                    window.hideLoading();

                    // عرض إشعار نجاح إذا طُلب
                    if (showNotification && window.showSuccess) {
                        window.showSuccess('تم حفظ البيانات بنجاح', 'تم الحفظ');
                    }

                } catch (error) {
                    window.hideLoading();
                    console.error('خطأ في حفظ البيانات:', error);

                    if (window.showError) {
                        window.showError('حدث خطأ في حفظ البيانات. يرجى المحاولة مرة أخرى.', 'خطأ في الحفظ');
                    } else {
                        alert('حدث خطأ في حفظ البيانات. يرجى المحاولة مرة أخرى.');
                    }
                }
            }, 300); // تأخير قصير لإظهار المحمل

        } catch (error) {
            console.error('خطأ في حفظ البيانات:', error);
            if (window.showError) {
                window.showError('حدث خطأ في حفظ البيانات', 'خطأ');
            } else {
                alert('حدث خطأ في حفظ البيانات');
            }
        }
    };

    // دالة محسنة لتحميل البيانات
    window.loadFromStorage = function(key) {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : [];
        } catch (error) {
            console.error(`خطأ في تحميل البيانات من ${key}:`, error);
            return [];
        }
    };

    // دالة تنظيف البيانات التالفة
    function cleanupCorruptedData() {
        const keys = ['sbea_students', 'sbea_teachers', 'sbea_staff', 'sbea_groups', 'sbea_users', 'sbea_payments'];

        keys.forEach(key => {
            try {
                const data = localStorage.getItem(key);
                if (data) {
                    JSON.parse(data); // اختبار صحة JSON
                }
            } catch (error) {
                console.warn(`بيانات تالفة في ${key}، سيتم إعادة تعيينها`);
                localStorage.removeItem(key);
            }
        });
    }

    // تشغيل تنظيف البيانات عند التحميل
    cleanupCorruptedData();

    // دالة تحديث محسنة للإحصائيات
    function updateDashboardStats() {
        try {
            const students = loadFromStorage('sbea_students') || [];
            const teachers = loadFromStorage('sbea_teachers') || [];
            const staff = loadFromStorage('sbea_staff') || [];
            const groups = loadFromStorage('sbea_groups') || [];

            // تحديث عدد التلاميذ - الصفحة الرئيسية
            const totalStudentsElement = document.getElementById('total-students');
            if (totalStudentsElement) {
                totalStudentsElement.textContent = students.length;
            }

            // تحديث عدد التلاميذ - صفحات أخرى
            const studentCountElement = document.getElementById('student-count');
            if (studentCountElement) {
                studentCountElement.textContent = students.length;
            }

            // تحديث عدد الأساتذة - الصفحة الرئيسية
            const totalTeachersElement = document.getElementById('total-teachers');
            if (totalTeachersElement) {
                totalTeachersElement.textContent = teachers.length;
            }

            // تحديث عدد الأساتذة - صفحات أخرى
            const teacherCountElement = document.getElementById('teacher-count');
            if (teacherCountElement) {
                teacherCountElement.textContent = teachers.length;
            }

            // تحديث عدد الموظفين
            const staffCountElement = document.getElementById('staff-count');
            if (staffCountElement) {
                staffCountElement.textContent = staff.length;
            }

            // تحديث عدد المجموعات - الصفحة الرئيسية
            const totalGroupsElement = document.getElementById('total-groups');
            if (totalGroupsElement) {
                totalGroupsElement.textContent = groups.length;
            }

            // تحديث عدد المجموعات - صفحات أخرى
            const groupCountElement = document.getElementById('group-count');
            if (groupCountElement) {
                groupCountElement.textContent = groups.length;
            }

            // تحديث إحصائيات الدفع
            const paidStudents = students.filter(s => s.paid).length;
            const unpaidStudents = students.length - paidStudents;

            const paidCountElement = document.getElementById('paid-count');
            if (paidCountElement) {
                paidCountElement.textContent = paidStudents;
            }

            const unpaidCountElement = document.getElementById('unpaid-count');
            if (unpaidCountElement) {
                unpaidCountElement.textContent = unpaidStudents;
            }

            // تحديث الإحصائيات المالية
            const totalFees = students.reduce((sum, student) => {
                return sum + (parseFloat(student.fee) || 0);
            }, 0);

            const paidFees = students.reduce((sum, student) => {
                return sum + (student.paid ? (parseFloat(student.fee) || 0) : 0);
            }, 0);

            // الصفحة الرئيسية - إجمالي المستحقات
            const totalFeesElement = document.getElementById('total-fees');
            if (totalFeesElement) {
                totalFeesElement.textContent = `${totalFees.toLocaleString()} DHS`;
            }

            // الصفحة الرئيسية - المستحقات المدفوعة
            const paidFeesElement = document.getElementById('paid-fees');
            if (paidFeesElement) {
                paidFeesElement.textContent = `${paidFees.toLocaleString()} DHS`;
            }

            // صفحات أخرى - إجمالي الإيرادات
            const revenueElement = document.getElementById('total-revenue');
            if (revenueElement) {
                revenueElement.textContent = `${totalFees.toLocaleString()} DHS`;
            }

            // تحديث النسب المئوية
            const totalStudents = students.length;
            if (totalStudents > 0) {
                const paidPercentage = Math.round((paidStudents / totalStudents) * 100);
                const unpaidPercentage = 100 - paidPercentage;

                const paidPercentElement = document.getElementById('paid-percentage');
                if (paidPercentElement) {
                    paidPercentElement.textContent = `${paidPercentage}%`;
                }

                const unpaidPercentElement = document.getElementById('unpaid-percentage');
                if (unpaidPercentElement) {
                    unpaidPercentElement.textContent = `${unpaidPercentage}%`;
                }
            }

        } catch (error) {
            console.error('خطأ في تحديث الإحصائيات:', error);
        }
    }

    // دالة التحقق من تحميل المكتبات الخارجية
    function checkExternalLibraries() {
        const libraries = {
            'JsBarcode': typeof JsBarcode !== 'undefined',
            'XLSX': typeof XLSX !== 'undefined'
        };

        const missingLibraries = Object.entries(libraries)
            .filter(([name, loaded]) => !loaded)
            .map(([name]) => name);

        if (missingLibraries.length > 0) {
            console.warn('المكتبات التالية غير محملة:', missingLibraries);

            // إظهار تحذير للمستخدم
            if (missingLibraries.includes('JsBarcode')) {
                console.warn('مكتبة الرموز الشريطية غير محملة - قد لا تعمل الرموز الشريطية بشكل صحيح');
            }
            if (missingLibraries.includes('XLSX')) {
                console.warn('مكتبة Excel غير محملة - قد لا تعمل ميزات الاستيراد والتصدير');
            }
        }

        return libraries;
    }

    // فحص المكتبات عند التحميل
    setTimeout(checkExternalLibraries, 1000);

    // =================================================================
    //                      Expenses Page Functions
    // =================================================================
    function initExpensesPage() {
        // تهيئة فئات النفقات
        initializeExpenseCategories();

        // تعيين التاريخ الحالي
        const today = new Date().toISOString().split('T')[0];
        const expenseDateInput = document.getElementById('expense-date');
        if (expenseDateInput) {
            expenseDateInput.value = today;
        }

        // تعيين السنة الحالية في نموذج الميزانية
        const budgetYearInput = document.getElementById('budget-year');
        if (budgetYearInput) {
            budgetYearInput.value = new Date().getFullYear();
        }

        // تحميل فئات النفقات في القوائم المنسدلة
        loadExpenseCategories();

        // تحميل النفقات وعرضها
        loadAndDisplayExpenses();

        // تحديث الإحصائيات
        updateExpensesStats();

        // إعداد التبويبات
        initExpensesTabs();

        // إعداد النماذج
        initExpenseForms();

        // إعداد الفلاتر
        initExpensesFilters();

        // إعداد النوافذ المنبثقة
        initExpensesModals();
    }

    // دالة إعداد التبويبات
    function initExpensesTabs() {
        const tabBtns = document.querySelectorAll('.expenses-tab-btn');
        const tabContents = document.querySelectorAll('.expenses-tab-content');

        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                tabBtns.forEach(b => b.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));

                btn.classList.add('active');
                const tabId = btn.dataset.tab;
                const targetTab = document.getElementById(`${tabId}-tab`);
                if (targetTab) {
                    targetTab.classList.add('active');
                }

                // تحديث المحتوى حسب التبويب
                switch (tabId) {
                    case 'daily-expenses':
                        loadAndDisplayExpenses();
                        updateExpensesStats();
                        break;
                    case 'categories':
                        displayExpenseCategories();
                        break;
                    case 'reports':
                        generateExpensesReport();
                        break;
                    case 'budget':
                        displayBudgetOverview();
                        break;
                }
            });
        });
    }

    // دالة تحميل فئات النفقات
    function loadExpenseCategories() {
        const categories = getFromStorage('sbea_expense_categories');
        const categorySelects = document.querySelectorAll('#expense-category, #filter-category');

        categorySelects.forEach(select => {
            if (select) {
                // الاحتفاظ بالخيار الأول
                const firstOption = select.querySelector('option[value=""]');
                select.innerHTML = '';
                if (firstOption) {
                    select.appendChild(firstOption);
                }

                categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name;
                    select.appendChild(option);
                });
            }
        });
    }

    // دالة إعداد النماذج
    function initExpenseForms() {
        // نموذج إضافة نفقة
        const addExpenseForm = document.getElementById('add-expense-form');
        if (addExpenseForm) {
            addExpenseForm.addEventListener('submit', handleAddExpense);
        }

        // نموذج إضافة فئة
        const addCategoryForm = document.getElementById('add-category-form');
        if (addCategoryForm) {
            addCategoryForm.addEventListener('submit', handleAddCategory);
        }

        // نموذج الميزانية
        const budgetForm = document.getElementById('budget-form');
        if (budgetForm) {
            budgetForm.addEventListener('submit', handleBudgetSubmit);
        }
    }

    // دالة معالجة إضافة نفقة جديدة
    function handleAddExpense(e) {
        e.preventDefault();

        const expenseData = {
            id: getNextExpenseId(),
            date: document.getElementById('expense-date').value,
            category: document.getElementById('expense-category').value,
            amount: parseFloat(document.getElementById('expense-amount').value),
            description: document.getElementById('expense-description').value,
            paymentMethod: document.getElementById('expense-payment-method').value,
            receipt: document.getElementById('expense-receipt').value || '',
            notes: document.getElementById('expense-notes').value || '',
            createdAt: new Date().toISOString(),
            createdBy: window.authSystem ? window.authSystem.getCurrentUser()?.username : 'مجهول'
        };

        // التحقق من صحة البيانات
        if (!expenseData.date || !expenseData.category || !expenseData.amount || !expenseData.description || !expenseData.paymentMethod) {
            window.showError('يرجى ملء جميع الحقول المطلوبة', 'خطأ في البيانات');
            return;
        }

        // حفظ النفقة
        const expenses = getFromStorage('sbea_expenses');
        expenses.push(expenseData);
        saveToStorage('sbea_expenses', expenses);

        // إعادة تعيين النموذج
        e.target.reset();
        document.getElementById('expense-date').value = new Date().toISOString().split('T')[0];

        // تحديث العرض
        loadAndDisplayExpenses();
        updateExpensesStats();

        // عرض رسالة نجاح
        window.showSuccess('تم إضافة النفقة بنجاح', 'تمت الإضافة');

        // تسجيل النشاط
        if (window.authSystem) {
            window.authSystem.logUserActivity('create', `إضافة نفقة: ${expenseData.description} - ${expenseData.amount} درهم`);
        }
    }

    // دالة تحميل وعرض النفقات
    function loadAndDisplayExpenses() {
        const expenses = getFromStorage('sbea_expenses');
        const expensesList = document.getElementById('expenses-list');

        if (!expensesList) return;

        if (expenses.length === 0) {
            expensesList.innerHTML = '<p class="no-data">لا توجد نفقات مسجلة بعد</p>';
            return;
        }

        // ترتيب النفقات حسب التاريخ (الأحدث أولاً)
        expenses.sort((a, b) => new Date(b.date) - new Date(a.date));

        let html = `
            <table class="expenses-table">
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>الفئة</th>
                        <th>الوصف</th>
                        <th>المبلغ</th>
                        <th>طريقة الدفع</th>
                        <th>رقم الإيصال</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
        `;

        const categories = getFromStorage('sbea_expense_categories');

        expenses.forEach(expense => {
            const category = categories.find(c => c.id == expense.category);
            const categoryName = category ? category.name : 'غير محدد';
            const paymentMethodNames = {
                'cash': 'نقداً',
                'bank': 'تحويل بنكي',
                'check': 'شيك',
                'card': 'بطاقة ائتمان'
            };

            html += `
                <tr>
                    <td>${new Date(expense.date).toLocaleDateString('ar-MA')}</td>
                    <td><span class="category-badge" style="background-color: ${category?.color || '#ccc'}">${categoryName}</span></td>
                    <td>${expense.description}</td>
                    <td class="amount">${expense.amount.toLocaleString()} DHS</td>
                    <td>${paymentMethodNames[expense.paymentMethod] || expense.paymentMethod}</td>
                    <td>${expense.receipt || '-'}</td>
                    <td class="actions">
                        <button onclick="editExpense(${expense.id})" class="edit-btn" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="deleteExpense(${expense.id})" class="delete-btn" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                        <button onclick="viewExpenseDetails(${expense.id})" class="view-btn" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table>';
        expensesList.innerHTML = html;
    }

    // دالة تحديث إحصائيات النفقات
    function updateExpensesStats() {
        const expenses = getFromStorage('sbea_expenses');
        const today = new Date();
        const todayStr = today.toISOString().split('T')[0];
        const currentMonth = today.getMonth();
        const currentYear = today.getFullYear();

        // حساب النفقات اليومية
        const todayExpenses = expenses.filter(e => e.date === todayStr);
        const todayTotal = todayExpenses.reduce((sum, e) => sum + e.amount, 0);
        const todayCount = todayExpenses.length;

        // حساب النفقات الشهرية
        const monthExpenses = expenses.filter(e => {
            const expenseDate = new Date(e.date);
            return expenseDate.getMonth() === currentMonth && expenseDate.getFullYear() === currentYear;
        });
        const monthTotal = monthExpenses.reduce((sum, e) => sum + e.amount, 0);

        // حساب النفقات السنوية
        const yearExpenses = expenses.filter(e => {
            const expenseDate = new Date(e.date);
            return expenseDate.getFullYear() === currentYear;
        });
        const yearTotal = yearExpenses.reduce((sum, e) => sum + e.amount, 0);

        // تحديث العناصر في الصفحة
        const todayTotalEl = document.getElementById('today-expenses-total');
        const monthTotalEl = document.getElementById('month-expenses-total');
        const yearTotalEl = document.getElementById('year-expenses-total');
        const todayCountEl = document.getElementById('today-expenses-count');

        if (todayTotalEl) todayTotalEl.textContent = `${todayTotal.toLocaleString()} DHS`;
        if (monthTotalEl) monthTotalEl.textContent = `${monthTotal.toLocaleString()} DHS`;
        if (yearTotalEl) yearTotalEl.textContent = `${yearTotal.toLocaleString()} DHS`;
        if (todayCountEl) todayCountEl.textContent = todayCount.toString();
    }

    // دالة إعداد الفلاتر
    function initExpensesFilters() {
        const applyFiltersBtn = document.getElementById('apply-filters-btn');
        const clearFiltersBtn = document.getElementById('clear-filters-btn');

        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', applyExpensesFilters);
        }

        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', clearExpensesFilters);
        }
    }

    // دالة تطبيق الفلاتر
    function applyExpensesFilters() {
        const dateFrom = document.getElementById('filter-date-from').value;
        const dateTo = document.getElementById('filter-date-to').value;
        const category = document.getElementById('filter-category').value;
        const paymentMethod = document.getElementById('filter-payment-method').value;

        let expenses = getFromStorage('sbea_expenses');

        // تطبيق فلاتر التاريخ
        if (dateFrom) {
            expenses = expenses.filter(e => e.date >= dateFrom);
        }
        if (dateTo) {
            expenses = expenses.filter(e => e.date <= dateTo);
        }

        // تطبيق فلتر الفئة
        if (category) {
            expenses = expenses.filter(e => e.category == category);
        }

        // تطبيق فلتر طريقة الدفع
        if (paymentMethod) {
            expenses = expenses.filter(e => e.paymentMethod === paymentMethod);
        }

        // عرض النتائج المفلترة
        displayFilteredExpenses(expenses);
    }

    // دالة عرض النفقات المفلترة
    function displayFilteredExpenses(expenses) {
        const expensesList = document.getElementById('expenses-list');

        if (!expensesList) return;

        if (expenses.length === 0) {
            expensesList.innerHTML = '<p class="no-data">لا توجد نفقات تطابق معايير البحث</p>';
            return;
        }

        // نفس منطق عرض النفقات العادية
        loadAndDisplayExpenses();
    }

    // إعداد النوافذ المنبثقة للنفقات
    function initExpensesModals() {
        // إعداد النوافذ المنبثقة العامة
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            const closeBtn = modal.querySelector('.close-modal');
            const cancelBtns = modal.querySelectorAll('.cancel-btn');

            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    modal.style.display = 'none';
                });
            }

            cancelBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    modal.style.display = 'none';
                });
            });

            // إغلاق عند النقر خارج النافذة
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.style.display = 'none';
                }
            });
        });
    }

    // دوال مساعدة للنفقات
    window.editExpense = function(expenseId) {
        const expenses = getFromStorage('sbea_expenses');
        const expense = expenses.find(e => e.id == expenseId);

        if (!expense) {
            window.showError('لم يتم العثور على النفقة المحددة', 'خطأ');
            return;
        }

        // ملء نموذج التعديل
        const modal = document.getElementById('edit-expense-modal');
        if (modal) {
            // ملء الحقول بالبيانات الحالية
            document.getElementById('edit-expense-id').value = expense.id;
            // ... ملء باقي الحقول

            modal.style.display = 'block';
        }
    };

    window.deleteExpense = function(expenseId) {
        const modal = document.getElementById('delete-expense-modal');
        if (modal) {
            const confirmBtn = document.getElementById('confirm-delete-btn');
            if (confirmBtn) {
                confirmBtn.onclick = () => {
                    const expenses = getFromStorage('sbea_expenses');
                    const expenseIndex = expenses.findIndex(e => e.id == expenseId);

                    if (expenseIndex > -1) {
                        const deletedExpense = expenses[expenseIndex];
                        expenses.splice(expenseIndex, 1);
                        saveToStorage('sbea_expenses', expenses);

                        // تحديث العرض
                        loadAndDisplayExpenses();
                        updateExpensesStats();

                        // تسجيل النشاط
                        if (window.autoLoggingSystem) {
                            window.autoLoggingSystem.log('حذف', `حذف نفقة: ${deletedExpense.description}`, {
                                expenseId: deletedExpense.id,
                                amount: deletedExpense.amount
                            });
                        }

                        window.showSuccess('تم حذف النفقة بنجاح', 'تم الحذف');
                    }

                    modal.style.display = 'none';
                };
            }

            modal.style.display = 'block';
        }
    };

    window.viewExpenseDetails = function(expenseId) {
        const expenses = getFromStorage('sbea_expenses');
        const expense = expenses.find(e => e.id == expenseId);

        if (!expense) {
            window.showError('لم يتم العثور على النفقة المحددة', 'خطأ');
            return;
        }

        // عرض تفاصيل النفقة في نافذة منبثقة
        const categories = getFromStorage('sbea_expense_categories');
        const category = categories.find(c => c.id == expense.category);

        const paymentMethodNames = {
            'cash': 'نقداً',
            'bank': 'تحويل بنكي',
            'check': 'شيك',
            'card': 'بطاقة ائتمان'
        };

        const detailsHtml = `
            <div class="expense-details">
                <h4>تفاصيل النفقة</h4>
                <div class="detail-row">
                    <strong>التاريخ:</strong> ${new Date(expense.date).toLocaleDateString('ar-MA')}
                </div>
                <div class="detail-row">
                    <strong>الفئة:</strong> ${category ? category.name : 'غير محدد'}
                </div>
                <div class="detail-row">
                    <strong>الوصف:</strong> ${expense.description}
                </div>
                <div class="detail-row">
                    <strong>المبلغ:</strong> ${expense.amount.toLocaleString()} درهم
                </div>
                <div class="detail-row">
                    <strong>طريقة الدفع:</strong> ${paymentMethodNames[expense.paymentMethod] || expense.paymentMethod}
                </div>
                ${expense.receipt ? `
                    <div class="detail-row">
                        <strong>رقم الإيصال:</strong> ${expense.receipt}
                    </div>
                ` : ''}
                ${expense.notes ? `
                    <div class="detail-row">
                        <strong>ملاحظات:</strong> ${expense.notes}
                    </div>
                ` : ''}
                <div class="detail-row">
                    <strong>تم الإنشاء بواسطة:</strong> ${expense.createdBy || 'غير محدد'}
                </div>
                <div class="detail-row">
                    <strong>تاريخ الإنشاء:</strong> ${new Date(expense.createdAt).toLocaleString('ar-MA')}
                </div>
            </div>
        `;

        // عرض في نافذة منبثقة مؤقتة
        const tempModal = document.createElement('div');
        tempModal.className = 'modal';
        tempModal.style.display = 'block';
        tempModal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>تفاصيل النفقة</h3>
                    <span class="close-modal">&times;</span>
                </div>
                <div class="modal-body">
                    ${detailsHtml}
                </div>
            </div>
        `;

        document.body.appendChild(tempModal);

        // إعداد إغلاق النافذة
        tempModal.querySelector('.close-modal').addEventListener('click', () => {
            tempModal.remove();
        });

        tempModal.addEventListener('click', (e) => {
            if (e.target === tempModal) {
                tempModal.remove();
            }
        });
    };

    // =================================================================
    //                      System Performance Enhancements
    // =================================================================

    // تحسين الأداء العام للنظام
    function enhanceSystemPerformance() {
        // تحسين التخزين المحلي
        optimizeLocalStorage();

        // تحسين استجابة الواجهة
        optimizeUIResponsiveness();

        // تحسين معالجة الأخطاء
        enhanceErrorHandling();

        // تحسين التحقق من صحة البيانات
        enhanceDataValidation();

        // تحسين الأمان
        enhanceSecurity();
    }

    // تحسين التخزين المحلي
    function optimizeLocalStorage() {
        // تنظيف البيانات القديمة
        const cleanupInterval = 24 * 60 * 60 * 1000; // 24 ساعة
        const lastCleanup = localStorage.getItem('sbea_last_cleanup');
        const now = Date.now();

        if (!lastCleanup || (now - parseInt(lastCleanup)) > cleanupInterval) {
            cleanupCorruptedData();
            localStorage.setItem('sbea_last_cleanup', now.toString());
        }

        // ضغط البيانات الكبيرة
        compressLargeData();

        // إنشاء نسخ احتياطية تلقائية
        createAutoBackup();
    }

    // ضغط البيانات الكبيرة
    function compressLargeData() {
        const keys = ['sbea_students', 'sbea_teachers', 'sbea_expenses', 'sbea_payments'];

        keys.forEach(key => {
            try {
                const data = localStorage.getItem(key);
                if (data && data.length > 50000) { // أكثر من 50KB
                    const parsed = JSON.parse(data);
                    // إزالة البيانات غير الضرورية
                    const compressed = removeUnnecessaryData(parsed, key);
                    localStorage.setItem(key, JSON.stringify(compressed));
                }
            } catch (error) {
                console.warn(`خطأ في ضغط البيانات لـ ${key}:`, error);
            }
        });
    }

    // إزالة البيانات غير الضرورية
    function removeUnnecessaryData(data, key) {
        if (!Array.isArray(data)) return data;

        return data.map(item => {
            const cleaned = { ...item };

            // إزالة الحقول الفارغة أو غير المستخدمة
            Object.keys(cleaned).forEach(field => {
                if (cleaned[field] === '' || cleaned[field] === null || cleaned[field] === undefined) {
                    delete cleaned[field];
                }
            });

            // ضغط الصور الكبيرة
            if (cleaned.picture && cleaned.picture.length > 100000) { // أكثر من 100KB
                cleaned.picture = compressImage(cleaned.picture);
            }

            return cleaned;
        });
    }

    // ضغط الصور
    function compressImage(base64Image) {
        try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            return new Promise((resolve) => {
                img.onload = () => {
                    const maxWidth = 300;
                    const maxHeight = 300;

                    let { width, height } = img;

                    if (width > height) {
                        if (width > maxWidth) {
                            height = (height * maxWidth) / width;
                            width = maxWidth;
                        }
                    } else {
                        if (height > maxHeight) {
                            width = (width * maxHeight) / height;
                            height = maxHeight;
                        }
                    }

                    canvas.width = width;
                    canvas.height = height;

                    ctx.drawImage(img, 0, 0, width, height);
                    resolve(canvas.toDataURL('image/jpeg', 0.7));
                };

                img.src = base64Image;
            });
        } catch (error) {
            console.warn('خطأ في ضغط الصورة:', error);
            return base64Image;
        }
    }

    // إنشاء نسخة احتياطية تلقائية
    function createAutoBackup() {
        const lastBackup = localStorage.getItem('sbea_last_auto_backup');
        const now = Date.now();
        const backupInterval = 7 * 24 * 60 * 60 * 1000; // أسبوع

        if (!lastBackup || (now - parseInt(lastBackup)) > backupInterval) {
            try {
                const backupData = {
                    students: getFromStorage('sbea_students'),
                    teachers: getFromStorage('sbea_teachers'),
                    staff: getFromStorage('sbea_staff'),
                    groups: getFromStorage('sbea_groups'),
                    expenses: getFromStorage('sbea_expenses'),
                    timestamp: new Date().toISOString(),
                    version: '2.0'
                };

                localStorage.setItem('sbea_auto_backup', JSON.stringify(backupData));
                localStorage.setItem('sbea_last_auto_backup', now.toString());

                console.log('تم إنشاء نسخة احتياطية تلقائية');
            } catch (error) {
                console.warn('خطأ في إنشاء النسخة الاحتياطية التلقائية:', error);
            }
        }
    }

    // تحسين استجابة الواجهة
    function optimizeUIResponsiveness() {
        // تحسين التمرير
        let ticking = false;

        function updateScrollEffects() {
            // تأثيرات التمرير المحسنة
            const scrollTop = window.pageYOffset;
            const header = document.querySelector('header');

            if (header) {
                if (scrollTop > 100) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }
            }

            ticking = false;
        }

        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollEffects);
                ticking = true;
            }
        });

        // تحسين تغيير حجم النافذة
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                // إعادة حساب التخطيط
                updateResponsiveLayout();
            }, 250);
        });
    }

    // تحديث التخطيط المتجاوب
    function updateResponsiveLayout() {
        const isMobile = window.innerWidth <= 768;
        const isTablet = window.innerWidth <= 1024 && window.innerWidth > 768;

        document.body.classList.toggle('mobile-layout', isMobile);
        document.body.classList.toggle('tablet-layout', isTablet);

        // تحديث الجداول للأجهزة المحمولة
        if (isMobile) {
            makeTablesResponsive();
        }
    }

    // جعل الجداول متجاوبة
    function makeTablesResponsive() {
        const tables = document.querySelectorAll('table');

        tables.forEach(table => {
            if (!table.closest('.table-responsive')) {
                const wrapper = document.createElement('div');
                wrapper.className = 'table-responsive';
                table.parentNode.insertBefore(wrapper, table);
                wrapper.appendChild(table);
            }
        });
    }

    // تحسين معالجة الأخطاء
    function enhanceErrorHandling() {
        // معالج الأخطاء العام
        window.addEventListener('error', (event) => {
            console.error('خطأ في النظام:', event.error);

            // إرسال تقرير الخطأ (اختياري)
            reportError(event.error);

            // عرض رسالة خطأ للمستخدم
            if (window.showError) {
                window.showError('حدث خطأ غير متوقع. يرجى إعادة تحميل الصفحة.', 'خطأ في النظام');
            }
        });

        // معالج الأخطاء للوعود
        window.addEventListener('unhandledrejection', (event) => {
            console.error('خطأ في الوعد:', event.reason);
            reportError(event.reason);
        });
    }

    // إرسال تقرير الخطأ
    function reportError(error) {
        try {
            const errorReport = {
                message: error.message || 'خطأ غير معروف',
                stack: error.stack || '',
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                url: window.location.href,
                user: window.authSystem ? window.authSystem.getCurrentUser()?.username : 'مجهول'
            };

            // حفظ التقرير محلياً
            const errorLogs = JSON.parse(localStorage.getItem('sbea_error_logs') || '[]');
            errorLogs.push(errorReport);

            // الاحتفاظ بآخر 50 خطأ فقط
            if (errorLogs.length > 50) {
                errorLogs.splice(0, errorLogs.length - 50);
            }

            localStorage.setItem('sbea_error_logs', JSON.stringify(errorLogs));
        } catch (e) {
            console.warn('خطأ في حفظ تقرير الخطأ:', e);
        }
    }

    // =================================================================
    //                      Logs Page Functions
    // =================================================================
    function initLogsPage() {
        // تحديث الإحصائيات
        updateLogsStatistics();

        // تحميل المستخدمين والصفحات في الفلاتر
        populateLogFilters();

        // عرض السجلات
        displayLogs();

        // إعداد معالجات الأحداث
        setupLogsEventHandlers();

        // إعداد التحديث التلقائي
        setupAutoRefresh();

        // إنشاء الرسوم البيانية
        createLogsCharts();
    }

    // تحديث إحصائيات السجلات
    function updateLogsStatistics() {
        if (!window.autoLoggingSystem) return;

        const stats = window.autoLoggingSystem.getStatistics();
        const today = new Date().toISOString().split('T')[0];

        // إحصائيات اليوم
        const todayLogs = window.autoLoggingSystem.getLogs({
            startDate: today + 'T00:00:00',
            endDate: today + 'T23:59:59'
        });

        const errorLogsToday = todayLogs.filter(log => log.type === 'خطأ').length;
        const activeUsers = new Set(todayLogs.map(log => log.userName)).size;

        // تحديث العناصر
        const totalLogsEl = document.getElementById('total-logs-count');
        const todayLogsEl = document.getElementById('today-logs-count');
        const activeUsersEl = document.getElementById('active-users-count');
        const errorsTodayEl = document.getElementById('errors-today-count');

        if (totalLogsEl) totalLogsEl.textContent = stats.totalLogs.toLocaleString();
        if (todayLogsEl) todayLogsEl.textContent = todayLogs.length.toLocaleString();
        if (activeUsersEl) activeUsersEl.textContent = activeUsers.toString();
        if (errorsTodayEl) errorsTodayEl.textContent = errorLogsToday.toString();
    }

    // ملء فلاتر السجلات
    function populateLogFilters() {
        if (!window.autoLoggingSystem) return;

        const logs = window.autoLoggingSystem.getLogs();

        // ملء قائمة المستخدمين
        const userSelect = document.getElementById('filter-user');
        const users = [...new Set(logs.map(log => log.userName))];

        if (userSelect) {
            userSelect.innerHTML = '<option value="">جميع المستخدمين</option>';
            users.forEach(user => {
                const option = document.createElement('option');
                option.value = user;
                option.textContent = user;
                userSelect.appendChild(option);
            });
        }

        // ملء قائمة الصفحات
        const pageSelect = document.getElementById('filter-page');
        const pages = [...new Set(logs.map(log => log.page))];

        if (pageSelect) {
            pageSelect.innerHTML = '<option value="">جميع الصفحات</option>';
            pages.forEach(page => {
                const option = document.createElement('option');
                option.value = page;
                option.textContent = page;
                pageSelect.appendChild(option);
            });
        }
    }

    // عرض السجلات
    function displayLogs(page = 1, pageSize = 50) {
        if (!window.autoLoggingSystem) return;

        // الحصول على الفلاتر
        const filters = getLogFilters();
        const logs = window.autoLoggingSystem.getLogs(filters);

        // حساب التصفح
        const totalLogs = logs.length;
        const totalPages = Math.ceil(totalLogs / pageSize);
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const paginatedLogs = logs.slice(startIndex, endIndex);

        // عرض السجلات
        const logsContainer = document.getElementById('logs-list');
        if (!logsContainer) return;

        if (paginatedLogs.length === 0) {
            logsContainer.innerHTML = '<p class="no-data">لا توجد سجلات تطابق معايير البحث</p>';
            return;
        }

        let html = `
            <table class="logs-table">
                <thead>
                    <tr>
                        <th>التاريخ والوقت</th>
                        <th>النوع</th>
                        <th>الإجراء</th>
                        <th>المستخدم</th>
                        <th>الصفحة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
        `;

        paginatedLogs.forEach(log => {
            const date = new Date(log.timestamp).toLocaleString('ar-MA');
            const typeClass = getLogTypeClass(log.type);

            html += `
                <tr>
                    <td>${date}</td>
                    <td><span class="log-type-badge ${typeClass}">${log.type}</span></td>
                    <td>${log.action}</td>
                    <td>${log.userName}</td>
                    <td>${log.page}</td>
                    <td>
                        <button onclick="showLogDetails('${log.id}')" class="view-btn" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table>';
        logsContainer.innerHTML = html;

        // تحديث معلومات التصفح
        updatePaginationInfo(page, totalPages, totalLogs);
    }

    // الحصول على فلاتر السجلات
    function getLogFilters() {
        const filters = {};

        const typeFilter = document.getElementById('filter-log-type');
        const userFilter = document.getElementById('filter-user');
        const pageFilter = document.getElementById('filter-page');
        const dateFromFilter = document.getElementById('filter-date-from');
        const dateToFilter = document.getElementById('filter-date-to');
        const searchFilter = document.getElementById('filter-search');

        if (typeFilter && typeFilter.value) filters.type = typeFilter.value;
        if (userFilter && userFilter.value) filters.userId = userFilter.value;
        if (pageFilter && pageFilter.value) filters.page = pageFilter.value;
        if (dateFromFilter && dateFromFilter.value) filters.startDate = dateFromFilter.value;
        if (dateToFilter && dateToFilter.value) filters.endDate = dateToFilter.value;
        if (searchFilter && searchFilter.value) filters.search = searchFilter.value;

        return filters;
    }

    // الحصول على فئة CSS لنوع السجل
    function getLogTypeClass(type) {
        const typeClasses = {
            'إنشاء': 'success',
            'تحديث': 'info',
            'حذف': 'danger',
            'تسجيل دخول': 'primary',
            'تسجيل خروج': 'secondary',
            'دفعة': 'success',
            'تصدير': 'info',
            'استيراد': 'info',
            'طباعة': 'secondary',
            'عرض': 'light',
            'بحث': 'light',
            'خطأ': 'danger',
            'تحذير': 'warning',
            'معلومات': 'info'
        };

        return typeClasses[type] || 'secondary';
    }

    // إعداد معالجات أحداث السجلات
    function setupLogsEventHandlers() {
        const applyFiltersBtn = document.getElementById('apply-log-filters-btn');
        const clearFiltersBtn = document.getElementById('clear-log-filters-btn');
        const refreshBtn = document.getElementById('refresh-logs-btn');

        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', () => {
                displayLogs();
            });
        }

        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', () => {
                document.getElementById('filter-log-type').value = '';
                document.getElementById('filter-user').value = '';
                document.getElementById('filter-page').value = '';
                document.getElementById('filter-date-from').value = '';
                document.getElementById('filter-date-to').value = '';
                document.getElementById('filter-search').value = '';
                displayLogs();
            });
        }

        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                updateLogsStatistics();
                populateLogFilters();
                displayLogs();
            });
        }

        // أزرار التصدير
        const exportJsonBtn = document.getElementById('export-logs-json-btn');
        const exportCsvBtn = document.getElementById('export-logs-csv-btn');

        if (exportJsonBtn) {
            exportJsonBtn.addEventListener('click', () => {
                if (window.autoLoggingSystem) {
                    window.autoLoggingSystem.exportLogs('json');
                }
            });
        }

        if (exportCsvBtn) {
            exportCsvBtn.addEventListener('click', () => {
                if (window.autoLoggingSystem) {
                    window.autoLoggingSystem.exportLogs('csv');
                }
            });
        }

        // أزرار إدارة السجلات
        const clearLogsBtn = document.getElementById('clear-logs-btn');
        const toggleLoggingBtn = document.getElementById('toggle-logging-btn');

        if (clearLogsBtn) {
            clearLogsBtn.addEventListener('click', () => {
                document.getElementById('clear-logs-modal').style.display = 'block';
            });
        }

        if (toggleLoggingBtn) {
            toggleLoggingBtn.addEventListener('click', () => {
                if (window.autoLoggingSystem) {
                    const isEnabled = window.autoLoggingSystem.isEnabled;
                    window.autoLoggingSystem.setEnabled(!isEnabled);
                    toggleLoggingBtn.innerHTML = isEnabled ?
                        '<i class="fas fa-play"></i> تشغيل التسجيل' :
                        '<i class="fas fa-pause"></i> إيقاف التسجيل';
                    toggleLoggingBtn.className = isEnabled ? 'action-btn success' : 'action-btn warning';
                }
            });
        }

        // تأكيد مسح السجلات
        const confirmClearBtn = document.getElementById('confirm-clear-logs-btn');
        if (confirmClearBtn) {
            confirmClearBtn.addEventListener('click', () => {
                if (window.autoLoggingSystem) {
                    window.autoLoggingSystem.clearLogs();
                    document.getElementById('clear-logs-modal').style.display = 'none';
                    updateLogsStatistics();
                    displayLogs();
                    window.showSuccess('تم مسح جميع السجلات بنجاح', 'تم المسح');
                }
            });
        }
    }

    // إعداد التحديث التلقائي للسجلات
    function setupAutoRefresh() {
        // تحديث الإحصائيات كل دقيقة
        setInterval(() => {
            updateLogsStatistics();
        }, 60000);

        // تحديث السجلات كل 30 ثانية
        setInterval(() => {
            const currentPage = parseInt(document.getElementById('page-info')?.textContent?.match(/\d+/)?.[0] || '1');
            displayLogs(currentPage);
        }, 30000);
    }

    // إنشاء الرسوم البيانية للسجلات
    function createLogsCharts() {
        if (!window.autoLoggingSystem || typeof Chart === 'undefined') {
            console.warn('Chart.js غير متوفر أو نظام السجلات غير مفعل');
            return;
        }

        const stats = window.autoLoggingSystem.getStatistics();

        // رسم بياني للأنشطة حسب النوع
        const typeChartCtx = document.getElementById('logs-by-type-chart');
        if (typeChartCtx) {
            new Chart(typeChartCtx, {
                type: 'doughnut',
                data: {
                    labels: Object.keys(stats.logsByType),
                    datasets: [{
                        data: Object.values(stats.logsByType),
                        backgroundColor: [
                            '#007bff', '#28a745', '#dc3545', '#ffc107',
                            '#17a2b8', '#6f42c1', '#e83e8c', '#fd7e14'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // رسم بياني للمستخدمين الأكثر نشاطاً
        const userChartCtx = document.getElementById('logs-by-user-chart');
        if (userChartCtx) {
            const topUsers = Object.entries(stats.logsByUser)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 5);

            new Chart(userChartCtx, {
                type: 'bar',
                data: {
                    labels: topUsers.map(([user]) => user),
                    datasets: [{
                        label: 'عدد الأنشطة',
                        data: topUsers.map(([,count]) => count),
                        backgroundColor: '#007bff'
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // رسم بياني للأنشطة حسب الوقت (آخر 7 أيام)
        const timeChartCtx = document.getElementById('logs-by-time-chart');
        if (timeChartCtx) {
            const logs = window.autoLoggingSystem.getLogs();
            const last7Days = [];
            const today = new Date();

            for (let i = 6; i >= 0; i--) {
                const date = new Date(today);
                date.setDate(date.getDate() - i);
                const dateStr = date.toISOString().split('T')[0];

                const dayLogs = logs.filter(log =>
                    log.timestamp.startsWith(dateStr)
                ).length;

                last7Days.push({
                    date: date.toLocaleDateString('ar-MA'),
                    count: dayLogs
                });
            }

            new Chart(timeChartCtx, {
                type: 'line',
                data: {
                    labels: last7Days.map(day => day.date),
                    datasets: [{
                        label: 'عدد الأنشطة',
                        data: last7Days.map(day => day.count),
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    }

    // تحديث معلومات التصفح
    function updatePaginationInfo(currentPage, totalPages, totalItems) {
        const pageInfo = document.getElementById('page-info');
        const prevBtn = document.getElementById('prev-page-btn');
        const nextBtn = document.getElementById('next-page-btn');

        if (pageInfo) {
            pageInfo.textContent = `الصفحة ${currentPage} من ${totalPages} (${totalItems} سجل)`;
        }

        if (prevBtn) {
            prevBtn.disabled = currentPage <= 1;
            prevBtn.onclick = () => {
                if (currentPage > 1) {
                    displayLogs(currentPage - 1);
                }
            };
        }

        if (nextBtn) {
            nextBtn.disabled = currentPage >= totalPages;
            nextBtn.onclick = () => {
                if (currentPage < totalPages) {
                    displayLogs(currentPage + 1);
                }
            };
        }
    }

    // عرض تفاصيل السجل
    window.showLogDetails = function(logId) {
        if (!window.autoLoggingSystem) return;

        const logs = window.autoLoggingSystem.getLogs();
        const log = logs.find(l => l.id === logId);

        if (!log) return;

        const modal = document.getElementById('log-details-modal');
        const content = document.getElementById('log-details-content');

        if (modal && content) {
            content.innerHTML = `
                <div class="log-details">
                    <div class="detail-row">
                        <strong>معرف السجل:</strong> ${log.id}
                    </div>
                    <div class="detail-row">
                        <strong>النوع:</strong> <span class="log-type-badge ${getLogTypeClass(log.type)}">${log.type}</span>
                    </div>
                    <div class="detail-row">
                        <strong>الإجراء:</strong> ${log.action}
                    </div>
                    <div class="detail-row">
                        <strong>المستخدم:</strong> ${log.userName} (${log.userId})
                    </div>
                    <div class="detail-row">
                        <strong>الصفحة:</strong> ${log.page}
                    </div>
                    <div class="detail-row">
                        <strong>التاريخ والوقت:</strong> ${new Date(log.timestamp).toLocaleString('ar-MA')}
                    </div>
                    <div class="detail-row">
                        <strong>معرف الجلسة:</strong> ${log.sessionId}
                    </div>
                    <div class="detail-row">
                        <strong>متصفح المستخدم:</strong> ${log.userAgent}
                    </div>
                    ${Object.keys(log.details).length > 0 ? `
                        <div class="detail-row">
                            <strong>التفاصيل:</strong>
                            <pre class="details-json">${JSON.stringify(log.details, null, 2)}</pre>
                        </div>
                    ` : ''}
                </div>
            `;

            modal.style.display = 'block';
        }
    };

    // =================================================================
    //                      Teacher Students Page Functions
    // =================================================================
    function initTeacherStudentsPage() {
        // تحميل بيانات الأستاذ الحالي
        loadCurrentTeacherData();

        // ملء الفلاتر
        populateTeacherFilters();

        // عرض التلاميذ
        displayStudentsGrid();

        // تحديث الإحصائيات
        updateTeacherStats();

        // إعداد معالجات الأحداث
        setupTeacherEventHandlers();

        // إعداد النوافذ المنبثقة
        setupTeacherModals();
    }

    // تحميل بيانات الأستاذ الحالي
    function loadCurrentTeacherData() {
        // الحصول على بيانات الأستاذ من نظام المصادقة
        const currentUser = window.authSystem ? window.authSystem.getCurrentUser() : null;
        if (!currentUser || currentUser.role !== 'teacher') {
            window.location.href = 'login.html';
            return;
        }

        // تحديث معلومات الأستاذ في الواجهة
        const userNameEl = document.querySelector('.current-user-name');
        const userRoleEl = document.querySelector('.current-user-role');

        if (userNameEl) userNameEl.textContent = currentUser.username;
        if (userRoleEl) userRoleEl.textContent = 'أستاذ';
    }

    // ملء فلاتر الأساتذة
    function populateTeacherFilters() {
        const students = getFromStorage('sbea_students');
        const groups = getFromStorage('sbea_groups');

        // ملء قائمة المستويات
        const levelSelect = document.getElementById('filter-level');
        const levels = [...new Set(students.map(s => s.level))];

        if (levelSelect) {
            levelSelect.innerHTML = '<option value="">جميع المستويات</option>';
            levels.forEach(level => {
                const option = document.createElement('option');
                option.value = level;
                option.textContent = level;
                levelSelect.appendChild(option);
            });
        }

        // ملء قائمة الأفواج
        const groupSelect = document.getElementById('filter-group');
        const groupNames = [...new Set(students.map(s => s.group))];

        if (groupSelect) {
            groupSelect.innerHTML = '<option value="">جميع الأفواج</option>';
            groupNames.forEach(group => {
                const option = document.createElement('option');
                option.value = group;
                option.textContent = group;
                groupSelect.appendChild(option);
            });
        }

        // ملء قائمة المواد
        const subjectSelects = document.querySelectorAll('#filter-subject, #grade-subject, #note-subject, #attendance-subject');
        const subjects = window.gradesAndNotesSystem ? window.gradesAndNotesSystem.subjects : [
            'الرياضيات', 'اللغة العربية', 'اللغة الفرنسية', 'اللغة الإنجليزية',
            'النشاط العلمي', 'الاجتماعيات', 'التربية الإسلامية'
        ];

        subjectSelects.forEach(select => {
            if (select) {
                const firstOption = select.querySelector('option[value=""]');
                select.innerHTML = '';
                if (firstOption) {
                    select.appendChild(firstOption);
                }

                subjects.forEach(subject => {
                    const option = document.createElement('option');
                    option.value = subject;
                    option.textContent = subject;
                    select.appendChild(option);
                });
            }
        });
    }

    // عرض التلاميذ في الشبكة
    function displayStudentsGrid() {
        const students = getFromStorage('sbea_students');
        const studentsGrid = document.getElementById('students-grid');

        if (!studentsGrid) return;

        // تطبيق الفلاتر
        const filteredStudents = applyTeacherFilters(students);

        if (filteredStudents.length === 0) {
            studentsGrid.innerHTML = '<p class="no-data">لا توجد تلاميذ تطابق معايير البحث</p>';
            return;
        }

        let html = '';
        filteredStudents.forEach(student => {
            const average = window.gradesAndNotesSystem ?
                window.gradesAndNotesSystem.calculateStudentAverage(student.barcode) : 0;
            const recentNotes = window.gradesAndNotesSystem ?
                window.gradesAndNotesSystem.getStudentNotes(student.barcode).slice(0, 3) : [];

            html += `
                <div class="student-card" data-barcode="${student.barcode}">
                    <div class="student-photo">
                        ${student.picture ?
                            `<img src="${student.picture}" alt="${student.name}">` :
                            `<div class="no-photo"><i class="fas fa-user"></i></div>`
                        }
                    </div>
                    <div class="student-info">
                        <h4>${student.name}</h4>
                        <p><strong>الرقم:</strong> ${student.barcode}</p>
                        <p><strong>المستوى:</strong> ${student.level}</p>
                        <p><strong>الفوج:</strong> ${student.group}</p>
                        <p><strong>المتوسط:</strong> <span class="average-badge">${average}/20</span></p>
                    </div>
                    <div class="student-actions">
                        <button onclick="openStudentDetails('${student.barcode}')" class="action-btn info" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button onclick="openAddGradeModal('${student.barcode}')" class="action-btn success" title="إضافة نقطة">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button onclick="openAddNoteModal('${student.barcode}')" class="action-btn warning" title="إضافة ملاحظة">
                            <i class="fas fa-sticky-note"></i>
                        </button>
                    </div>
                    ${recentNotes.length > 0 ? `
                        <div class="recent-notes">
                            <small>آخر الملاحظات:</small>
                            ${recentNotes.map(note => `
                                <div class="note-preview ${note.type}">
                                    ${note.content.substring(0, 50)}${note.content.length > 50 ? '...' : ''}
                                </div>
                            `).join('')}
                        </div>
                    ` : ''}
                </div>
            `;
        });

        studentsGrid.innerHTML = html;
    }

    // تطبيق فلاتر الأساتذة
    function applyTeacherFilters(students) {
        let filtered = [...students];

        const levelFilter = document.getElementById('filter-level');
        const groupFilter = document.getElementById('filter-group');
        const subjectFilter = document.getElementById('filter-subject');
        const searchFilter = document.getElementById('search-student');

        if (levelFilter && levelFilter.value) {
            filtered = filtered.filter(s => s.level === levelFilter.value);
        }

        if (groupFilter && groupFilter.value) {
            filtered = filtered.filter(s => s.group === groupFilter.value);
        }

        if (searchFilter && searchFilter.value) {
            const searchTerm = searchFilter.value.toLowerCase();
            filtered = filtered.filter(s =>
                s.name.toLowerCase().includes(searchTerm) ||
                s.barcode.includes(searchTerm)
            );
        }

        return filtered;
    }

    // تحديث إحصائيات الأستاذ
    function updateTeacherStats() {
        const students = getFromStorage('sbea_students');
        const filteredStudents = applyTeacherFilters(students);

        // إجمالي التلاميذ
        const totalStudentsEl = document.getElementById('total-my-students');
        if (totalStudentsEl) {
            totalStudentsEl.textContent = filteredStudents.length.toString();
        }

        // متوسط النقاط
        if (window.gradesAndNotesSystem) {
            let totalAverage = 0;
            let studentsWithGrades = 0;

            filteredStudents.forEach(student => {
                const average = window.gradesAndNotesSystem.calculateStudentAverage(student.barcode);
                if (average > 0) {
                    totalAverage += parseFloat(average);
                    studentsWithGrades++;
                }
            });

            const overallAverage = studentsWithGrades > 0 ? (totalAverage / studentsWithGrades).toFixed(1) : '0.0';
            const averageGradesEl = document.getElementById('average-grades');
            if (averageGradesEl) {
                averageGradesEl.textContent = overallAverage;
            }
        }

        // الواجبات المعلقة (مؤقتاً 0)
        const pendingHomeworkEl = document.getElementById('pending-homework');
        if (pendingHomeworkEl) {
            pendingHomeworkEl.textContent = '0';
        }

        // الحضور اليوم (مؤقتاً 100%)
        const todayAttendanceEl = document.getElementById('today-attendance');
        if (todayAttendanceEl) {
            todayAttendanceEl.textContent = '100%';
        }
    }

    // إعداد معالجات أحداث الأساتذة
    function setupTeacherEventHandlers() {
        // فلاتر البحث
        const applyFiltersBtn = document.getElementById('apply-teacher-filters-btn');
        const clearFiltersBtn = document.getElementById('clear-teacher-filters-btn');

        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', () => {
                displayStudentsGrid();
                updateTeacherStats();
            });
        }

        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', () => {
                document.getElementById('filter-level').value = '';
                document.getElementById('filter-group').value = '';
                document.getElementById('filter-subject').value = '';
                document.getElementById('search-student').value = '';
                displayStudentsGrid();
                updateTeacherStats();
            });
        }

        // أزرار الإجراءات السريعة
        const addGradeBtn = document.getElementById('add-grade-btn');
        const addNoteBtn = document.getElementById('add-note-btn');
        const markAttendanceBtn = document.getElementById('mark-attendance-btn');

        if (addGradeBtn) {
            addGradeBtn.addEventListener('click', () => {
                document.getElementById('add-grade-modal').style.display = 'block';
                populateStudentSelects();
            });
        }

        if (addNoteBtn) {
            addNoteBtn.addEventListener('click', () => {
                document.getElementById('add-note-modal').style.display = 'block';
                populateStudentSelects();
            });
        }

        if (markAttendanceBtn) {
            markAttendanceBtn.addEventListener('click', () => {
                document.getElementById('attendance-modal').style.display = 'block';
                setupAttendanceModal();
            });
        }

        // تبديل العرض
        const gridViewBtn = document.getElementById('grid-view-btn');
        const listViewBtn = document.getElementById('list-view-btn');

        if (gridViewBtn && listViewBtn) {
            gridViewBtn.addEventListener('click', () => {
                gridViewBtn.classList.add('active');
                listViewBtn.classList.remove('active');
                document.getElementById('students-grid').style.display = 'grid';
                document.getElementById('students-list').style.display = 'none';
            });

            listViewBtn.addEventListener('click', () => {
                listViewBtn.classList.add('active');
                gridViewBtn.classList.remove('active');
                document.getElementById('students-grid').style.display = 'none';
                document.getElementById('students-list').style.display = 'block';
                displayStudentsList();
            });
        }
    }

    // إعداد النوافذ المنبثقة للأساتذة
    function setupTeacherModals() {
        // إعداد النوافذ المنبثقة العامة
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            const closeBtn = modal.querySelector('.close-modal');
            const cancelBtns = modal.querySelectorAll('.cancel-btn');

            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    modal.style.display = 'none';
                });
            }

            cancelBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    modal.style.display = 'none';
                });
            });

            // إغلاق عند النقر خارج النافذة
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.style.display = 'none';
                }
            });
        });

        // تعيين التاريخ الحالي في النماذج
        const today = new Date().toISOString().split('T')[0];
        const dateInputs = document.querySelectorAll('#grade-date, #note-date, #attendance-date');
        dateInputs.forEach(input => {
            if (input) input.value = today;
        });
    }

    // ملء قوائم التلاميذ في النماذج
    function populateStudentSelects() {
        const students = getFromStorage('sbea_students');
        const studentSelects = document.querySelectorAll('#grade-student, #note-student');

        studentSelects.forEach(select => {
            if (select) {
                select.innerHTML = '<option value="">اختر التلميذ</option>';
                students.forEach(student => {
                    const option = document.createElement('option');
                    option.value = student.barcode;
                    option.textContent = `${student.name} - ${student.barcode}`;
                    select.appendChild(option);
                });
            }
        });
    }

    // عرض التلاميذ في قائمة
    function displayStudentsList() {
        const students = getFromStorage('sbea_students');
        const studentsList = document.getElementById('students-list');

        if (!studentsList) return;

        const filteredStudents = applyTeacherFilters(students);

        if (filteredStudents.length === 0) {
            studentsList.innerHTML = '<p class="no-data">لا توجد تلاميذ تطابق معايير البحث</p>';
            return;
        }

        let html = `
            <table class="students-table">
                <thead>
                    <tr>
                        <th>الصورة</th>
                        <th>الاسم</th>
                        <th>الرقم التسلسلي</th>
                        <th>المستوى</th>
                        <th>الفوج</th>
                        <th>المتوسط</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
        `;

        filteredStudents.forEach(student => {
            const average = window.gradesAndNotesSystem ?
                window.gradesAndNotesSystem.calculateStudentAverage(student.barcode) : 0;

            html += `
                <tr>
                    <td>
                        ${student.picture ?
                            `<img src="${student.picture}" alt="${student.name}" class="student-photo-small">` :
                            `<div class="no-photo-small"><i class="fas fa-user"></i></div>`
                        }
                    </td>
                    <td>${student.name}</td>
                    <td>${student.barcode}</td>
                    <td>${student.level}</td>
                    <td>${student.group}</td>
                    <td><span class="average-badge">${average}/20</span></td>
                    <td class="actions">
                        <button onclick="openStudentDetails('${student.barcode}')" class="action-btn info" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button onclick="openAddGradeModal('${student.barcode}')" class="action-btn success" title="إضافة نقطة">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button onclick="openAddNoteModal('${student.barcode}')" class="action-btn warning" title="إضافة ملاحظة">
                            <i class="fas fa-sticky-note"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table>';
        studentsList.innerHTML = html;
    }

    // دوال مساعدة للنوافذ المنبثقة
    window.openStudentDetails = function(studentBarcode) {
        const students = getFromStorage('sbea_students');
        const student = students.find(s => s.barcode === studentBarcode);

        if (!student) return;

        const modal = document.getElementById('student-details-modal');
        const content = document.getElementById('student-details-content');

        if (modal && content) {
            // عرض تفاصيل التلميذ
            const grades = window.gradesAndNotesSystem ?
                window.gradesAndNotesSystem.getStudentGrades(studentBarcode) : [];
            const notes = window.gradesAndNotesSystem ?
                window.gradesAndNotesSystem.getStudentNotes(studentBarcode) : [];

            content.innerHTML = `
                <div class="student-details">
                    <div class="student-header">
                        ${student.picture ?
                            `<img src="${student.picture}" alt="${student.name}" class="student-photo-large">` :
                            `<div class="no-photo-large"><i class="fas fa-user"></i></div>`
                        }
                        <div class="student-basic-info">
                            <h3>${student.name}</h3>
                            <p><strong>الرقم التسلسلي:</strong> ${student.barcode}</p>
                            <p><strong>المستوى:</strong> ${student.level}</p>
                            <p><strong>الفوج:</strong> ${student.group}</p>
                            <p><strong>الهاتف:</strong> ${student.phone || 'غير محدد'}</p>
                        </div>
                    </div>

                    <div class="student-tabs">
                        <button class="tab-btn active" data-tab="grades">النقاط</button>
                        <button class="tab-btn" data-tab="notes">الملاحظات</button>
                    </div>

                    <div id="grades-tab" class="tab-content active">
                        <h4>النقاط والتقييمات</h4>
                        ${grades.length > 0 ? `
                            <table class="grades-table">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>المادة</th>
                                        <th>النوع</th>
                                        <th>النقطة</th>
                                        <th>المعامل</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${grades.map(grade => `
                                        <tr>
                                            <td>${new Date(grade.date).toLocaleDateString('ar-MA')}</td>
                                            <td>${grade.subject}</td>
                                            <td>${grade.type}</td>
                                            <td>${grade.value}/${grade.maxValue}</td>
                                            <td>${grade.coefficient}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        ` : '<p>لا توجد نقاط مسجلة بعد</p>'}
                    </div>

                    <div id="notes-tab" class="tab-content">
                        <h4>الملاحظات</h4>
                        ${notes.length > 0 ? `
                            <div class="notes-list">
                                ${notes.map(note => `
                                    <div class="note-item ${note.type}">
                                        <div class="note-header">
                                            <span class="note-type">${note.type}</span>
                                            <span class="note-date">${new Date(note.date).toLocaleDateString('ar-MA')}</span>
                                        </div>
                                        <div class="note-content">${note.content}</div>
                                        ${note.subject ? `<div class="note-subject">المادة: ${note.subject}</div>` : ''}
                                    </div>
                                `).join('')}
                            </div>
                        ` : '<p>لا توجد ملاحظات مسجلة بعد</p>'}
                    </div>
                </div>
            `;

            // إعداد التبويبات
            const tabBtns = content.querySelectorAll('.tab-btn');
            const tabContents = content.querySelectorAll('.tab-content');

            tabBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    tabBtns.forEach(b => b.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));

                    btn.classList.add('active');
                    const tabId = btn.dataset.tab;
                    const targetTab = content.querySelector(`#${tabId}-tab`);
                    if (targetTab) {
                        targetTab.classList.add('active');
                    }
                });
            });

            modal.style.display = 'block';
        }
    };

    window.openAddGradeModal = function(studentBarcode = '') {
        const modal = document.getElementById('add-grade-modal');
        if (modal) {
            populateStudentSelects();
            if (studentBarcode) {
                const studentSelect = document.getElementById('grade-student');
                if (studentSelect) {
                    studentSelect.value = studentBarcode;
                }
            }
            modal.style.display = 'block';
        }
    };

    window.openAddNoteModal = function(studentBarcode = '') {
        const modal = document.getElementById('add-note-modal');
        if (modal) {
            populateStudentSelects();
            if (studentBarcode) {
                const studentSelect = document.getElementById('note-student');
                if (studentSelect) {
                    studentSelect.value = studentBarcode;
                }
            }
            modal.style.display = 'block';
        }
    };

    // تطبيق التحسينات عند تحميل الصفحة
    enhanceSystemPerformance();

    // =================================================================
    //                      Receipt Printing Functions
    // =================================================================

    // دالة طباعة وصل الدفع المحسنة
    window.printPaymentReceipt = function(studentBarcode, month, amount, paymentMethod = 'نقداً', notes = '') {
        const students = getFromStorage('sbea_students');
        const student = students.find(s => s.barcode === studentBarcode);

        if (!student) {
            window.showError('لم يتم العثور على التلميذ', 'خطأ');
            return;
        }

        // استخدام نظام الطباعة المحسن
        if (window.receiptPrintingSystem) {
            const paymentData = {
                studentName: student.name,
                studentBarcode: student.barcode,
                studentLevel: student.level,
                studentGroup: student.group,
                amount: amount,
                month: month,
                paymentMethod: paymentMethod,
                notes: notes,
                paymentDate: new Date().toISOString()
            };

            window.receiptPrintingSystem.printPaymentReceipt(paymentData);

            // تسجيل عملية الطباعة
            if (window.autoLoggingSystem) {
                window.autoLoggingSystem.log('طباعة', `طباعة وصل دفع للتلميذ: ${student.name}`, {
                    studentBarcode: student.barcode,
                    month: month,
                    amount: amount,
                    paymentMethod: paymentMethod
                });
            }
        } else {
            // النظام القديم كبديل
            printBasicReceipt(student, month, amount, paymentMethod);
        }
    };

    // دالة طباعة أساسية كبديل
    function printBasicReceipt(student, month, amount, paymentMethod) {
        const receiptWindow = window.open('', '_blank');
        const receiptContent = `
            <!DOCTYPE html>
            <html dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>وصل استلام - ${student.name}</title>
                <style>
                    body { font-family: 'Tajawal', Arial, sans-serif; margin: 20px; direction: rtl; }
                    .receipt { max-width: 500px; margin: 0 auto; border: 2px solid #000; padding: 30px; }
                    .header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 15px; margin-bottom: 25px; }
                    .header h2 { margin: 0; font-size: 24px; color: #007bff; }
                    .header h3 { margin: 10px 0 0 0; font-size: 18px; }
                    .info { margin: 12px 0; display: flex; justify-content: space-between; border-bottom: 1px dotted #ccc; padding-bottom: 5px; }
                    .info strong { color: #333; }
                    .total { font-size: 20px; font-weight: bold; text-align: center; margin-top: 25px; border: 2px solid #000; padding: 15px; background: #f9f9f9; }
                    .footer { margin-top: 30px; text-align: center; font-size: 14px; }
                    .signature { margin-top: 40px; display: flex; justify-content: space-between; }
                    .signature div { text-align: center; border-top: 1px solid #000; padding-top: 10px; width: 45%; }
                    @media print { body { margin: 0; } }
                </style>
            </head>
            <body>
                <div class="receipt">
                    <div class="header">
                        <h2>مؤسسة النور التربوي للتعليم الخصوصي</h2>
                        <h3>وصل استلام</h3>
                    </div>
                    <div class="info"><strong>اسم التلميذ:</strong> <span>${student.name}</span></div>
                    <div class="info"><strong>الرقم التسلسلي:</strong> <span>${student.barcode}</span></div>
                    <div class="info"><strong>المستوى:</strong> <span>${student.level}</span></div>
                    <div class="info"><strong>الفوج:</strong> <span>${student.group}</span></div>
                    <div class="info"><strong>الشهر:</strong> <span>${month}</span></div>
                    <div class="info"><strong>طريقة الدفع:</strong> <span>${paymentMethod}</span></div>
                    <div class="info"><strong>التاريخ:</strong> <span>${new Date().toLocaleDateString('ar-MA')}</span></div>
                    <div class="info"><strong>الوقت:</strong> <span>${new Date().toLocaleTimeString('ar-MA')}</span></div>
                    <div class="total">المبلغ المدفوع: ${amount} درهم مغربي</div>
                    <div class="signature">
                        <div>توقيع المستلم</div>
                        <div>توقيع المسؤول</div>
                    </div>
                    <div class="footer">
                        <p>شكراً لكم على ثقتكم - مؤسسة النور التربوي</p>
                        <p style="font-size: 12px; color: #666;">تم الإنشاء تلقائياً بتاريخ ${new Date().toLocaleString('ar-MA')}</p>
                    </div>
                </div>
                <script>
                    window.onload = function() {
                        setTimeout(function() {
                            window.print();
                        }, 500);
                    };
                </script>
            </body>
            </html>
        `;

        receiptWindow.document.write(receiptContent);
        receiptWindow.document.close();
    }

    // =================================================================
    //                      System Health Check
    // =================================================================

    // فحص صحة النظام عند التحميل
    function performSystemHealthCheck() {
        console.log('🔍 بدء فحص صحة النظام...');

        // تشغيل التشخيص الشامل
        if (window.systemDiagnostics) {
            window.systemDiagnostics.runDiagnostics();
            const report = window.systemDiagnostics.getDiagnosticsReport();

            console.log('📊 تقرير صحة النظام:', report);

            // عرض تحذيرات إذا كانت هناك مشاكل
            if (report.systemHealth === 'critical') {
                console.error('🚨 النظام في حالة حرجة! يرجى مراجعة المشاكل.');

                // محاولة الإصلاح التلقائي
                const fixedIssues = window.systemDiagnostics.autoFix();
                if (fixedIssues > 0) {
                    console.log(`✅ تم إصلاح ${fixedIssues} مشكلة تلقائياً`);
                    // إعادة تشغيل التشخيص
                    window.systemDiagnostics.runDiagnostics();
                }
            } else if (report.systemHealth === 'warning') {
                console.warn('⚠️ النظام يحتوي على تحذيرات، يُنصح بالمراجعة.');
            } else {
                console.log('✅ النظام يعمل بشكل جيد');
            }
        }

        console.log('✅ انتهى فحص صحة النظام');
    }

    // دالة فحص وإصلاح شاملة
    window.performSystemMaintenance = function() {
        console.log('🔧 بدء صيانة النظام الشاملة...');

        // فحص صحة النظام
        performSystemHealthCheck();

        // تنظيف شامل
        const cleanedItems = cleanupCorruptedData();

        // تحسين الأداء
        enhanceSystemPerformance();

        console.log('✅ انتهت صيانة النظام بنجاح');

        return {
            cleanedItems: cleanedItems,
            timestamp: new Date().toISOString(),
            status: 'completed'
        };
    };

    // تشغيل فحص صحة النظام عند التحميل
    setTimeout(() => {
        performSystemHealthCheck();
    }, 1000);

    // تصدير الدوال للاستخدام العام
    window.updateDashboardStats = updateDashboardStats;
    window.cleanupCorruptedData = cleanupCorruptedData;
    window.checkExternalLibraries = checkExternalLibraries;
    window.enhanceSystemPerformance = enhanceSystemPerformance;
    window.performSystemHealthCheck = performSystemHealthCheck;
    window.openPaymentsModal = openPaymentsModal;
});